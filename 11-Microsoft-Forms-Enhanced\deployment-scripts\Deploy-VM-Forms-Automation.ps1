# Deploy-VM-Forms-Automation.ps1
# PowerShell script for automated VM deployment from Microsoft Forms
# Integrates with Power Automate and Azure DevOps pipeline

param(
    [Parameter(Mandatory = $true)]
    [string]$VmName,
    
    [Parameter(Mandatory = $true)]
    [string]$HospitalCode,
    
    [Parameter(Mandatory = $true)]
    [string]$DepartmentCode,
    
    [string]$FirstName = "",
    
    [string]$LastName = "",
    
    [string]$DeploymentId = "",
    
    [string]$RequestedBy = "System Request",
    
    [ValidateSet("plan", "apply", "destroy")]
    [string]$Action = "apply",
    
    [int]$VmCount = 1,
    
    [string]$VmSize = "Standard_D4s_v3",
    
    [string]$Location = "East US 2",
    
    [switch]$AutoApprove = $true,
    
    [switch]$SendNotifications = $true
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Initialize logging
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage -ForegroundColor $(
        switch ($Level) {
            "ERROR" { "Red" }
            "WARN" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
    )
}

# Validate form inputs
function Test-FormInputs {
    param(
        [string]$Hospital,
        [string]$Department,
        [string]$VmName
    )
    
    $errors = @()
    
    if ([string]::IsNullOrEmpty($Hospital)) {
        $errors += "Hospital code is required"
    } elseif ($Hospital.Length -gt 4) {
        $errors += "Hospital code must be 4 characters or less"
    } elseif ($Hospital -notmatch '^[A-Z0-9]+$') {
        $errors += "Hospital code must contain only letters and numbers"
    }
    
    if ([string]::IsNullOrEmpty($Department)) {
        $errors += "Department code is required"
    } elseif ($Department.Length -gt 4) {
        $errors += "Department code must be 4 characters or less"
    } elseif ($Department -notmatch '^[A-Z]+$') {
        $errors += "Department code must contain only letters"
    }
    
    if ([string]::IsNullOrEmpty($VmName)) {
        $errors += "VM name is required"
    } elseif ($VmName.Length -gt 15) {
        $errors += "VM name must be 15 characters or less"
    }
    
    return $errors
}

# Generate deployment metadata
function New-DeploymentMetadata {
    return @{
        DeploymentId = if ($DeploymentId) { $DeploymentId } else { "VM-$(Get-Date -Format 'yyyyMMddHHmmss')-$((New-Guid).ToString().Substring(0,8))" }
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss UTC"
        VmName = $VmName
        HospitalCode = $HospitalCode.ToUpper()
        DepartmentCode = $DepartmentCode.ToUpper()
        RequestedBy = $RequestedBy
        Action = $Action
        VmCount = $VmCount
        VmSize = $VmSize
        Location = $Location
    }
}

# Create Terraform variables file
function New-TerraformVars {
    param([hashtable]$Metadata)
    
    $tfvarsContent = @"
# Generated by Microsoft Forms Automation
# Deployment ID: $($Metadata.DeploymentId)
# Generated: $($Metadata.Timestamp)

# Core VM Configuration
vm_name = "$($Metadata.VmName)"
vm_count = $($Metadata.VmCount)
vm_resourcegroup = "MAH9-SUP-CCH9-VMC"
vm_size = "$($Metadata.VmSize)"
vm_sku = "2022-datacenter"
vm_location = "$($Metadata.Location)"

# Authentication
vm_admin_username = "maoperator"
vm_admin_password = "M1t1g@t0r2025"

# Azure DevOps Integration
azure_devops_organization = "https://dev.azure.com/MAHealth"
azure_devops_teamproject = "MAH9-SCP-CCH9GI"
azure_devops_deploymentgroup = "MAH9-SCP-CCH9-DGR-DEV"
azure_devops_pat = "4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7"
azure_devops_agentfolder = "c:/Agent"

# Form Metadata
deployment_id = "$($Metadata.DeploymentId)"
hospital_code = "$($Metadata.HospitalCode)"
department_code = "$($Metadata.DepartmentCode)"
requested_by = "$($Metadata.RequestedBy)"
request_timestamp = "$($Metadata.Timestamp)"

# Common Tags
common_tags = {
  Environment = "Production"
  Project = "Hospital-VM-Automation"
  ManagedBy = "Terraform"
  Source = "Microsoft-Forms"
  DeploymentId = "$($Metadata.DeploymentId)"
  HospitalCode = "$($Metadata.HospitalCode)"
  DepartmentCode = "$($Metadata.DepartmentCode)"
  RequestedBy = "$($Metadata.RequestedBy)"
}
"@
    
    $tfvarsPath = "terraform.tfvars"
    $tfvarsContent | Out-File -FilePath $tfvarsPath -Encoding UTF8
    Write-Log "Created terraform.tfvars file" "SUCCESS"
    
    return $tfvarsPath
}

# Execute Terraform command with error handling
function Invoke-TerraformCommand {
    param(
        [string]$Command,
        [string[]]$Arguments = @(),
        [int]$TimeoutMinutes = 30
    )
    
    $fullCommand = "terraform $Command $($Arguments -join ' ')"
    Write-Log "Executing: $fullCommand"
    
    try {
        $process = Start-Process -FilePath "terraform" -ArgumentList ($Command, $Arguments) -NoNewWindow -Wait -PassThru -RedirectStandardOutput "terraform_output.log" -RedirectStandardError "terraform_error.log"
        
        if ($process.ExitCode -eq 0) {
            Write-Log "Terraform $Command completed successfully" "SUCCESS"
            return $true
        } else {
            $errorOutput = Get-Content "terraform_error.log" -Raw
            Write-Log "Terraform $Command failed with exit code $($process.ExitCode)" "ERROR"
            Write-Log "Error output: $errorOutput" "ERROR"
            return $false
        }
    } catch {
        Write-Log "Failed to execute terraform $Command : $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main deployment function
function Start-VMDeployment {
    param([hashtable]$Metadata)
    
    Write-Log "🚀 Starting VM deployment for $($Metadata.DeploymentId)" "SUCCESS"
    Write-Log "VM Name: $($Metadata.VmName)"
    Write-Log "Hospital: $($Metadata.HospitalCode)"
    Write-Log "Department: $($Metadata.DepartmentCode)"
    Write-Log "Requested by: $($Metadata.RequestedBy)"
    Write-Log "Action: $($Metadata.Action)"
    
    # Check Terraform installation
    try {
        $terraformVersion = terraform version
        Write-Log "Terraform version: $($terraformVersion[0])" "SUCCESS"
    } catch {
        Write-Log "Terraform is not installed or not in PATH" "ERROR"
        throw "Terraform installation required"
    }
    
    # Change to Terraform directory
    $terraformDir = "01-Terraform-Core"
    if (Test-Path $terraformDir) {
        Set-Location $terraformDir
        Write-Log "Changed to Terraform directory: $terraformDir" "SUCCESS"
    } else {
        Write-Log "Terraform directory not found: $terraformDir" "ERROR"
        throw "Terraform directory not found"
    }
    
    # Initialize Terraform
    Write-Log "Initializing Terraform..."
    if (!(Invoke-TerraformCommand "init")) {
        throw "Terraform initialization failed"
    }
    
    # Execute the requested action
    switch ($Action) {
        "plan" {
            Write-Log "Running Terraform plan..."
            if (!(Invoke-TerraformCommand "plan" @("-var-file=../terraform.tfvars", "-out=tfplan"))) {
                throw "Terraform plan failed"
            }
        }
        "apply" {
            Write-Log "Running Terraform apply..."
            $applyArgs = @("-var-file=../terraform.tfvars")
            if ($AutoApprove) {
                $applyArgs += "-auto-approve"
            }
            
            if (!(Invoke-TerraformCommand "apply" $applyArgs)) {
                throw "Terraform apply failed"
            }
            
            Write-Log "VM deployment completed successfully!" "SUCCESS"
        }
        "destroy" {
            Write-Log "Running Terraform destroy..."
            $destroyArgs = @("-var-file=../terraform.tfvars")
            if ($AutoApprove) {
                $destroyArgs += "-auto-approve"
            }
            
            if (!(Invoke-TerraformCommand "destroy" $destroyArgs)) {
                throw "Terraform destroy failed"
            }
            
            Write-Log "VM destruction completed successfully!" "SUCCESS"
        }
    }
}

# Main execution
try {
    Write-Log "🏥 Microsoft Forms VM Deployment Automation" "SUCCESS"
    Write-Log "================================================"
    
    # Validate inputs
    $validationErrors = Test-FormInputs -Hospital $HospitalCode -Department $DepartmentCode -VmName $VmName
    if ($validationErrors.Count -gt 0) {
        Write-Log "Validation errors found:" "ERROR"
        foreach ($error in $validationErrors) {
            Write-Log "  - $error" "ERROR"
        }
        throw "Input validation failed"
    }
    
    # Generate deployment metadata
    $metadata = New-DeploymentMetadata
    Write-Log "Generated deployment metadata for: $($metadata.DeploymentId)" "SUCCESS"
    
    # Create Terraform variables
    $tfvarsPath = New-TerraformVars -Metadata $metadata
    
    # Execute deployment
    Start-VMDeployment -Metadata $metadata
    
    Write-Log "🎉 Deployment completed successfully!" "SUCCESS"
    Write-Log "Deployment ID: $($metadata.DeploymentId)"
    Write-Log "VM Name: $($metadata.VmName)"
    
} catch {
    Write-Log "❌ Deployment failed: $($_.Exception.Message)" "ERROR"
    exit 1
} finally {
    # Cleanup temporary files
    if (Test-Path "terraform_output.log") { Remove-Item "terraform_output.log" -Force }
    if (Test-Path "terraform_error.log") { Remove-Item "terraform_error.log" -Force }
}
