# 🚀 Automatic VM Deployment - Complete Setup Guide

## ✨ What This Does

This solution provides a **web interface that automatically deploys VMs** without any manual intervention. Just fill out the form, click deploy, and watch your VM get created in real-time!

## 🎯 Features

- ✅ **Automatic Deployment** - No manual PowerShell execution needed
- ✅ **Real-time Progress** - Watch deployment progress live
- ✅ **Live Logs** - See Terraform output in real-time
- ✅ **Multiple Actions** - Plan, Apply, and Destroy
- ✅ **Status Monitoring** - Track deployment status and duration
- ✅ **Error Handling** - Clear error messages and troubleshooting

## 🔧 Quick Setup (5 Minutes)

### Step 1: Install Prerequisites

**Install Node.js:**
- Download from: https://nodejs.org/
- Or use Chocolatey: `choco install nodejs`

**Verify Terraform and Azure CLI:**
```bash
terraform --version
az --version
az login  # Make sure you're authenticated
```

### Step 2: Run Setup

**Option A: Automatic Setup (Recommended)**
```cmd
# Double-click this file or run from command prompt
setup-auto-deploy.bat
```

**Option B: Manual Setup**
```bash
# Install dependencies
npm install

# Start the server
npm start
```

### Step 3: Access the Web Interface

1. **Open your browser**
2. **Go to: http://localhost:3000**
3. **Fill out the form**
4. **Click "🚀 Deploy VM Now"**
5. **Watch it deploy automatically!**

## 🎮 How to Use

### 1. Start the Server

```bash
# Start the web server
npm start

# Or for development (auto-restart)
npm run dev
```

You'll see:
```
🚀 Terraform Web Server running on http://localhost:3000
📁 Working directory: D:\Maspects\MAHealth\Terraform\Scope_Demo-Environment
⚡ Ready to deploy VMs automatically!
```

### 2. Open the Web Interface

- **URL:** http://localhost:3000
- **Form:** Pre-filled with your working configuration
- **Actions:** Plan, Deploy, or Destroy

### 3. Deploy a VM

1. **Customize parameters** (VM name, count, size, etc.)
2. **Click "🚀 Deploy VM Now"**
3. **Watch real-time progress:**
   - ✅ Creating terraform.tfvars
   - ✅ Initializing Terraform
   - ✅ Validating configuration
   - ✅ Running plan
   - ✅ Applying changes
   - ✅ VM created successfully!

### 4. Monitor Progress

The interface shows:
- **Status:** Starting → Running → Completed/Failed
- **Progress Bar:** Visual progress indicator
- **Real-time Logs:** Live Terraform output
- **Duration:** How long the deployment takes
- **Details:** VM name, action, timestamps

## 📊 Example Deployment Flow

```
🔄 User fills form and clicks "Deploy"
    ↓
⚡ Server receives request and starts deployment
    ↓
📝 Creates terraform.tfvars with form data
    ↓
🔧 Runs: terraform init
    ↓
✅ Runs: terraform validate
    ↓
📋 Runs: terraform plan
    ↓
🚀 Runs: terraform apply -auto-approve
    ↓
✅ VM deployed successfully!
    ↓
📊 Shows completion status and details
```

## 🎯 Deployment Options

### Plan Deployment (Safe)
- **Action:** Plan
- **What it does:** Shows what will be created
- **Safe:** No resources created
- **Use for:** Validation and review

### Deploy VM (Creates Resources)
- **Action:** Apply
- **What it does:** Creates actual Azure resources
- **Cost:** Will incur Azure charges
- **Use for:** Actual VM deployment

### Destroy Infrastructure
- **Action:** Destroy
- **What it does:** Removes all created resources
- **Safe:** Cleans up resources
- **Use for:** Cleanup and cost savings

## 🔍 Real-time Monitoring

### Status Updates
- **Starting:** Deployment initiated
- **Running:** Terraform executing
- **Completed:** VM deployed successfully
- **Failed:** Error occurred (with details)

### Live Logs
- **Real-time output** from Terraform
- **Timestamped entries** for tracking
- **Error messages** for troubleshooting
- **Auto-scroll** to latest entries

### Progress Tracking
- **Visual progress bar**
- **Duration tracking**
- **Step-by-step status**
- **Completion notifications**

## 🛠️ Troubleshooting

### Server Won't Start

**Error:** `node: command not found`
```bash
# Install Node.js
# Download from: https://nodejs.org/
```

**Error:** `Cannot find module 'express'`
```bash
# Install dependencies
npm install
```

### Deployment Fails

**Error:** `terraform: command not found`
```bash
# Install Terraform
choco install terraform
# Or download from: https://terraform.io/downloads
```

**Error:** `Azure authentication failed`
```bash
# Login to Azure
az login
az account set --subscription "your-subscription-id"
```

**Error:** `Resource group not found`
```bash
# Create resource group
az group create --name "MAH9-SUP-CCH9-VMC" --location "East US 2"
```

### Form Issues

**Error:** `Server not available`
- **Solution:** Make sure the server is running (`npm start`)
- **Check:** http://localhost:3000/api/health

**Error:** `Deployment stuck`
- **Solution:** Check the logs for specific errors
- **Action:** Refresh the page and try again

## 📋 Configuration

### Default Settings
- **VM Size:** Standard_D4s_v3 (4 vCPUs, 16GB RAM)
- **OS:** Windows Server 2022 Datacenter
- **Location:** East US 2
- **Resource Group:** MAH9-SUP-CCH9-VMC

### Customizable Parameters
- **VM Name:** Base name for your VMs
- **VM Count:** Number of VMs (1-10)
- **VM Size:** Choose from available sizes
- **Windows Version:** 2019/2022 options
- **Azure Region:** Multiple regions available
- **Admin Credentials:** Username and password
- **Azure DevOps:** Organization, project, deployment group

## 🔒 Security Notes

### Local Server
- **Runs locally** on your machine
- **No external access** by default
- **Your credentials** stay on your machine

### Azure Authentication
- **Uses your Azure CLI** authentication
- **No credentials stored** in the application
- **Standard Azure security** practices

### Sensitive Data
- **Passwords** are handled securely
- **PAT tokens** are not logged
- **Form data** is not persisted

## 🚀 Advanced Usage

### Multiple Deployments
```javascript
// Deploy multiple VMs with different configurations
// The web interface handles this automatically
```

### Custom Configurations
```javascript
// Modify terraform-web-server.js for custom logic
// Add new VM sizes, regions, or parameters
```

### Integration
```javascript
// The server provides REST APIs for integration:
// POST /api/deploy - Start deployment
// GET /api/status/:id - Check status
// GET /api/deployments - List all deployments
```

## 📞 Support

### Common Commands
```bash
# Start server
npm start

# Check health
curl http://localhost:3000/api/health

# View logs
# Check the web interface logs section
```

### Log Files
- **Server logs:** Console output
- **Terraform logs:** Visible in web interface
- **Azure logs:** Azure portal activity log

### Getting Help
1. **Check the troubleshooting section**
2. **Review server console output**
3. **Check Azure portal for resource status**
4. **Verify all prerequisites are installed**

---

## 🎉 Success!

You now have a **fully automated VM deployment system**! 

- ✅ **No manual PowerShell execution**
- ✅ **Real-time progress monitoring**
- ✅ **Professional web interface**
- ✅ **Error handling and logging**
- ✅ **Multiple deployment options**

**Just fill out the form and click deploy - your VM will be created automatically!** 🚀
