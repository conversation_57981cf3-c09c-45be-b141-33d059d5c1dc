# PowerShell script for Power Automate to deploy Terraform
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("plan", "apply", "destroy")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [int]$VmCount = 1,
    
    [Parameter(Mandatory=$false)]
    [string]$VmName = "CCH9APPVMCT",
    
    [Parameter(Mandatory=$true)]
    [string]$SubscriptionId,
    
    [Parameter(Mandatory=$true)]
    [string]$TenantId,
    
    [Parameter(Mandatory=$true)]
    [string]$ClientId,
    
    [Parameter(Mandatory=$true)]
    [string]$ClientSecret,
    
    [Parameter(Mandatory=$false)]
    [string]$StorageAccountName = "terraformstatestorage",
    
    [Parameter(Mandatory=$false)]
    [string]$StorageContainerName = "tfstate",
    
    [Parameter(Mandatory=$false)]
    [string]$ResourceGroupName = "MAH9-SUP-CCH9-VMC"
)

# Function to write logs
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Output "[$timestamp] [$Level] $Message"
}

try {
    Write-Log "Starting Terraform deployment process..."
    Write-Log "Action: $Action, VM Count: $VmCount, VM Name: $VmName"

    # Set environment variables for Terraform Azure authentication
    $env:ARM_CLIENT_ID = $ClientId
    $env:ARM_CLIENT_SECRET = $ClientSecret
    $env:ARM_SUBSCRIPTION_ID = $SubscriptionId
    $env:ARM_TENANT_ID = $TenantId

    # Check if Terraform is installed
    $terraformVersion = terraform --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Log "Terraform not found. Installing Terraform..." "WARNING"
        
        # Install Terraform using Chocolatey
        if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
            Write-Log "Installing Chocolatey..."
            Set-ExecutionPolicy Bypass -Scope Process -Force
            [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
            iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
        }
        
        choco install terraform -y
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to install Terraform"
        }
    } else {
        Write-Log "Terraform found: $terraformVersion"
    }

    # Navigate to Terraform directory
    $terraformDir = $PSScriptRoot
    Set-Location $terraformDir
    Write-Log "Working directory: $terraformDir"

    # Initialize Terraform with backend configuration
    Write-Log "Initializing Terraform..."
    $initArgs = @(
        "init",
        "-backend-config=storage_account_name=$StorageAccountName",
        "-backend-config=container_name=$StorageContainerName",
        "-backend-config=key=terraform.tfstate",
        "-backend-config=resource_group_name=$ResourceGroupName"
    )
    
    & terraform $initArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Terraform init failed"
    }

    # Create terraform.tfvars file with dynamic values
    $tfvarsContent = @"
vm_count = $VmCount
vm_name = "$VmName"
vm_resourcegroup = "$ResourceGroupName"
"@
    
    $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
    Write-Log "Created terraform.tfvars with custom values"

    # Execute Terraform action
    switch ($Action) {
        "plan" {
            Write-Log "Running Terraform plan..."
            & terraform plan -var-file="terraform.tfvars" -out="tfplan"
            if ($LASTEXITCODE -ne 0) {
                throw "Terraform plan failed"
            }
            Write-Log "Terraform plan completed successfully"
        }
        "apply" {
            Write-Log "Running Terraform apply..."
            & terraform apply -var-file="terraform.tfvars" -auto-approve
            if ($LASTEXITCODE -ne 0) {
                throw "Terraform apply failed"
            }
            Write-Log "Terraform apply completed successfully"
            
            # Get outputs
            Write-Log "Getting Terraform outputs..."
            $outputs = terraform output -json | ConvertFrom-Json
            Write-Log "Terraform outputs: $($outputs | ConvertTo-Json -Depth 3)"
        }
        "destroy" {
            Write-Log "Running Terraform destroy..."
            & terraform destroy -var-file="terraform.tfvars" -auto-approve
            if ($LASTEXITCODE -ne 0) {
                throw "Terraform destroy failed"
            }
            Write-Log "Terraform destroy completed successfully"
        }
    }

    Write-Log "Terraform deployment process completed successfully" "SUCCESS"
    
    # Return success status for Power Automate
    return @{
        Status = "Success"
        Action = $Action
        VmCount = $VmCount
        VmName = $VmName
        Message = "Terraform $Action completed successfully"
    }

} catch {
    Write-Log "Error occurred: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    
    # Return error status for Power Automate
    return @{
        Status = "Failed"
        Action = $Action
        VmCount = $VmCount
        VmName = $VmName
        Message = $_.Exception.Message
        Error = $_.ScriptStackTrace
    }
} finally {
    # Cleanup
    if (Test-Path "terraform.tfvars") {
        Remove-Item "terraform.tfvars" -Force
        Write-Log "Cleaned up terraform.tfvars file"
    }
}
