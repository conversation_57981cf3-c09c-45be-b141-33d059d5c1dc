# Auto-Deploy VM Script - No Manual Steps Required
param(
    [string]$VmName = "CCH9APPVMCT",
    [int]$VmCount = 1,
    [ValidateSet("plan", "apply", "destroy")]
    [string]$Action = "apply",
    [switch]$Interactive = $true
)

Clear-Host
Write-Host "========================================"
Write-Host "    VM Auto-Deploy (Simplified)"
Write-Host "========================================"
Write-Host ""

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Yellow

if (!(Test-Path "main.tf")) {
    Write-Host "ERROR: main.tf not found!" -ForegroundColor Red
    Write-Host "Please run this script from your Terraform directory." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "Found main.tf" -ForegroundColor Green

if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
    Write-Host "ERROR: Terraform not found!" -ForegroundColor Red
    Write-Host "Please install Terraform and ensure it's in your PATH" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "Terraform is available" -ForegroundColor Green

Write-Host "All prerequisites met!" -ForegroundColor Green
Write-Host ""

# Interactive mode for parameter collection
if ($Interactive) {
    Write-Host "========================================"
    Write-Host "VM Configuration"
    Write-Host "========================================"
    
    $VmNameInput = Read-Host "VM Name (default: $VmName)"
    if (![string]::IsNullOrEmpty($VmNameInput)) { $VmName = $VmNameInput }
    
    $VmCountInput = Read-Host "VM Count (default: $VmCount)"
    if (![string]::IsNullOrEmpty($VmCountInput)) { $VmCount = [int]$VmCountInput }
    
    Write-Host ""
    Write-Host "Available actions:" -ForegroundColor Yellow
    Write-Host "  plan    - Show what will be created (safe)" -ForegroundColor White
    Write-Host "  apply   - Create the VM (costs money)" -ForegroundColor White
    Write-Host "  destroy - Remove all resources" -ForegroundColor White
    Write-Host ""
    
    $ActionInput = Read-Host "Action (plan/apply/destroy) [default: $Action]"
    if (![string]::IsNullOrEmpty($ActionInput)) { $Action = $ActionInput }
}

# Configuration summary
Write-Host ""
Write-Host "========================================"
Write-Host "Deployment Configuration"
Write-Host "========================================"
Write-Host "VM Name: $VmName"
Write-Host "VM Count: $VmCount"
Write-Host "Action: $Action"
Write-Host "Resource Group: MAH9-SUP-CCH9-VMC"
Write-Host "VM Size: Standard_D4s_v3"
Write-Host "Location: East US 2"
Write-Host "========================================"
Write-Host ""

# Confirmation for destructive actions
if ($Action -eq "apply") {
    Write-Host "WARNING: This will create real Azure resources and may incur costs!" -ForegroundColor Yellow
    $confirm = Read-Host "Are you sure you want to proceed? (yes/no)"
    if ($confirm -ne "yes") {
        Write-Host "Deployment cancelled." -ForegroundColor Yellow
        exit 0
    }
}

if ($Action -eq "destroy") {
    Write-Host "WARNING: This will destroy all infrastructure!" -ForegroundColor Red
    $confirm = Read-Host "Are you sure you want to destroy resources? (yes/no)"
    if ($confirm -ne "yes") {
        Write-Host "Destroy cancelled." -ForegroundColor Yellow
        exit 0
    }
}

Write-Host ""
Write-Host "Starting automatic deployment..." -ForegroundColor Green
Write-Host ""

try {
    # Create terraform.tfvars automatically
    Write-Host "Creating terraform.tfvars..." -ForegroundColor Cyan
    
    $tfvarsContent = @"
vm_name = "$VmName"
vm_count = $VmCount
vm_resourcegroup = "MAH9-SUP-CCH9-VMC"
vm_size = "Standard_D4s_v3"
vm_sku = "2022-datacenter"
vm_location = "East US 2"
vm_admin_username = "maoperator"
vm_admin_password = "M1t1g@t0r2025"
azure_devops_organization = "https://dev.azure.com/MAHealth"
azure_devops_teamproject = "MAH9-SCP-CCH9GI"
azure_devops_deploymentgroup = "MAH9-SCP-CCH9-DGR-DEV"
azure_devops_pat = "4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7"
azure_devops_agentfolder = "c:/Agent"
"@
    
    $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
    Write-Host "terraform.tfvars created" -ForegroundColor Green
    
    # Initialize Terraform
    Write-Host "Initializing Terraform..." -ForegroundColor Cyan
    $initResult = terraform init
    if ($LASTEXITCODE -ne 0) {
        throw "Terraform init failed"
    }
    Write-Host "Terraform initialized" -ForegroundColor Green
    
    # Validate configuration
    Write-Host "Validating configuration..." -ForegroundColor Cyan
    $validateResult = terraform validate
    if ($LASTEXITCODE -ne 0) {
        throw "Terraform validation failed"
    }
    Write-Host "Configuration validated" -ForegroundColor Green
    
    # Execute the requested action
    switch ($Action) {
        "plan" {
            Write-Host "Running Terraform plan..." -ForegroundColor Cyan
            terraform plan -var-file="terraform.tfvars"
            if ($LASTEXITCODE -ne 0) {
                throw "Terraform plan failed"
            }
            Write-Host "Plan completed successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "Review the plan above. If it looks good, run this script again with '-Action apply'" -ForegroundColor Yellow
        }
        
        "apply" {
            Write-Host "Deploying VM(s)..." -ForegroundColor Cyan
            Write-Host "This may take 10-15 minutes..." -ForegroundColor Yellow
            
            terraform apply -var-file="terraform.tfvars" -auto-approve
            if ($LASTEXITCODE -ne 0) {
                throw "Terraform apply failed"
            }
            
            Write-Host ""
            Write-Host "VM deployment completed successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "Your VM details:" -ForegroundColor Cyan
            Write-Host "  VM Name: $VmName-1"
            Write-Host "  Resource Group: MAH9-SUP-CCH9-VMC"
            Write-Host "  Location: East US 2"
            Write-Host ""
            Write-Host "Next steps:" -ForegroundColor Yellow
            Write-Host "1. Check Azure Portal for your VM"
            Write-Host "2. Verify Azure DevOps agent registration"
            Write-Host "3. Connect to VM using RDP or Azure Bastion"
        }
        
        "destroy" {
            Write-Host "Destroying infrastructure..." -ForegroundColor Red
            terraform destroy -var-file="terraform.tfvars" -auto-approve
            if ($LASTEXITCODE -ne 0) {
                throw "Terraform destroy failed"
            }
            Write-Host "Infrastructure destroyed successfully!" -ForegroundColor Green
        }
    }
    
} catch {
    Write-Host ""
    Write-Host "Deployment failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Ensure you're logged into Azure (az login)"
    Write-Host "2. Verify Terraform is installed and in PATH"
    Write-Host "3. Check Azure permissions for the resource group"
    Write-Host "4. Review the error message above for specific issues"
}

Write-Host ""
Write-Host "========================================"
Write-Host "Deployment process completed"
Write-Host "========================================"
Write-Host ""
Read-Host "Press Enter to exit"
