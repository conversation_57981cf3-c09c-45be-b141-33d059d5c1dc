{"version": 4, "terraform_version": "1.8.0", "serial": 108, "lineage": "8b21bfc9-ae1d-9dd1-c073-9311a69cf001", "outputs": {}, "resources": [{"mode": "data", "type": "azurerm_resource_group", "name": "rg", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC", "location": "eastus2", "name": "MAH9-SUP-CCH9-VMC", "tags": {}, "timeouts": null}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "azurerm_network_interface", "name": "nic", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"applied_dns_servers": [], "dns_servers": [], "enable_accelerated_networking": false, "enable_ip_forwarding": false, "id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Network/networkInterfaces/CCH9APPVMCT2-nic1", "internal_dns_name_label": "", "internal_domain_name_suffix": "0b5y3bxg44yenpyqestr4hu2wg.cx.internal.cloudapp.net", "ip_configuration": [{"name": "internal", "primary": true, "private_ip_address": "********", "private_ip_address_allocation": "Dynamic", "private_ip_address_version": "IPv4", "public_ip_address_id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Network/publicIPAddresses/PIP-CCH9APPVMCT2-1", "subnet_id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Network/virtualNetworks/CCH9APPVMCT2-vNet/subnets/CCH9APPVMCT2-subnet"}], "location": "eastus2", "mac_address": "", "name": "CCH9APPVMCT2-nic1", "private_ip_address": "********", "private_ip_addresses": ["********"], "resource_group_name": "MAH9-SUP-CCH9-VMC", "tags": null, "timeouts": null, "virtual_machine_id": ""}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["azurerm_public_ip.example", "azurerm_subnet.subnet", "azurerm_virtual_network.vnet", "data.azurerm_resource_group.rg"]}]}, {"mode": "managed", "type": "azurerm_public_ip", "name": "example", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"allocation_method": "Dynamic", "domain_name_label": "cch9appvmct2-1", "fqdn": "cch9appvmct2-1.eastus2.cloudapp.azure.com", "id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Network/publicIPAddresses/PIP-CCH9APPVMCT2-1", "idle_timeout_in_minutes": 4, "ip_address": "", "ip_version": "IPv4", "location": "eastus2", "name": "PIP-CCH9APPVMCT2-1", "public_ip_prefix_id": null, "resource_group_name": "MAH9-SUP-CCH9-VMC", "reverse_fqdn": "", "sku": "Basic", "tags": null, "timeouts": null, "zones": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_resource_group.rg"]}]}, {"mode": "managed", "type": "azurerm_subnet", "name": "subnet", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefix": "********/24", "address_prefixes": ["********/24"], "delegation": [], "enforce_private_link_endpoint_network_policies": false, "enforce_private_link_service_network_policies": false, "id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Network/virtualNetworks/CCH9APPVMCT2-vNet/subnets/CCH9APPVMCT2-subnet", "name": "CCH9APPVMCT2-subnet", "resource_group_name": "MAH9-SUP-CCH9-VMC", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "CCH9APPVMCT2-vNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["azurerm_virtual_network.vnet", "data.azurerm_resource_group.rg"]}]}, {"mode": "managed", "type": "azurerm_virtual_machine_extension", "name": "customize", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"auto_upgrade_minor_version": false, "id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Compute/virtualMachines/CCH9APPVMCT2-1/extensions/customize-1", "name": "customize-1", "protected_settings": null, "publisher": "Microsoft.Compute", "settings": "{\"commandToExecute\":\"powershell.exe -ExecutionPolicy Unrestricted -Command \\\"Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1')); refreshenv; Import-Module $env:ChocolateyInstall\\\\helpers\\\\chocolateyProfile.psm1; Start-Sleep -Seconds 60; $env:Path = [System.Environment]::GetEnvironmentVariable('Path','Machine') + ';' + [System.Environment]::GetEnvironmentVariable('Path','User'); choco install azure-pipelines-agent --params '/Directory:c:/Agent /Token:4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7 /ProjectName:MAH9-SCP-CCH9GI /DeploymentGroup:true /DeploymentGroupName:MAH9-SCP-CCH9-DGR-DEV /DeploymentGroupTags:CCH9APPVMCT2-1 /LogonAccount:maoperator /LogonPassword:M1t1g@t0r2025 /Url:https://dev.azure.com/MAHealth' --confirm --force\\\"\"}", "tags": null, "timeouts": null, "type": "CustomScriptExtension", "type_handler_version": "1.10", "virtual_machine_id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Compute/virtualMachines/CCH9APPVMCT2-1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "protected_settings"}]], "private": "********************************************************************************************************************************************************************************", "dependencies": ["azurerm_network_interface.nic", "azurerm_public_ip.example", "azurerm_subnet.subnet", "azurerm_virtual_network.vnet", "azurerm_windows_virtual_machine.vm", "data.azurerm_resource_group.rg"]}]}, {"mode": "managed", "type": "azurerm_virtual_network", "name": "vnet", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_space": ["10.0.0.0/16"], "bgp_community": "", "ddos_protection_plan": [], "dns_servers": null, "guid": "868e7fd0-f7e6-46b0-bf10-24a71f1e9cb6", "id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Network/virtualNetworks/CCH9APPVMCT2-vNet", "location": "eastus2", "name": "CCH9APPVMCT2-vNet", "resource_group_name": "MAH9-SUP-CCH9-VMC", "subnet": [], "tags": null, "timeouts": null, "vm_protection_enabled": false}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["data.azurerm_resource_group.rg"]}]}, {"mode": "managed", "type": "azurerm_windows_virtual_machine", "name": "vm", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"additional_capabilities": [], "additional_unattend_content": [], "admin_password": "M1t1g@t0r2025", "admin_username": "maoperator", "allow_extension_operations": true, "availability_set_id": "", "boot_diagnostics": [], "computer_name": "CCH9APPVMCT2-1", "custom_data": null, "dedicated_host_id": "", "enable_automatic_updates": true, "encryption_at_host_enabled": false, "eviction_policy": "", "extensions_time_budget": "PT1H30M", "id": "/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Compute/virtualMachines/CCH9APPVMCT2-1", "identity": [], "license_type": "", "location": "eastus2", "max_bid_price": -1, "name": "CCH9APPVMCT2-1", "network_interface_ids": ["/subscriptions/41ae4571-862f-4682-ac9d-c8e39c9ce301/resourceGroups/MAH9-SUP-CCH9-VMC/providers/Microsoft.Network/networkInterfaces/CCH9APPVMCT2-nic1"], "os_disk": [{"caching": "ReadWrite", "diff_disk_settings": [], "disk_encryption_set_id": "", "disk_size_gb": 127, "name": "CCH9APPVMCT2-1_OsDisk_1_0265a36c557845c9b3f97a2584b7fefa", "storage_account_type": "Standard_LRS", "write_accelerator_enabled": false}], "patch_mode": "AutomaticByOS", "plan": [], "priority": "Regular", "private_ip_address": "********", "private_ip_addresses": ["********"], "provision_vm_agent": true, "proximity_placement_group_id": "", "public_ip_address": "************", "public_ip_addresses": ["************"], "resource_group_name": "MAH9-SUP-CCH9-VMC", "secret": [], "size": "Standard_D4s_v3", "source_image_id": "", "source_image_reference": [{"offer": "WindowsServer", "publisher": "MicrosoftWindowsServer", "sku": "2022-datacenter", "version": "latest"}], "tags": null, "timeouts": null, "timezone": "", "virtual_machine_id": "e15bc34a-889f-4234-98d1-cbc0e2922177", "virtual_machine_scale_set_id": "", "winrm_listener": [], "zone": ""}, "sensitive_attributes": [[{"type": "get_attr", "value": "custom_data"}], [{"type": "get_attr", "value": "admin_password"}]], "private": "********************************************************************************************************************************************************************************", "dependencies": ["azurerm_network_interface.nic", "azurerm_public_ip.example", "azurerm_subnet.subnet", "azurerm_virtual_network.vnet", "data.azurerm_resource_group.rg"]}]}], "check_results": null}