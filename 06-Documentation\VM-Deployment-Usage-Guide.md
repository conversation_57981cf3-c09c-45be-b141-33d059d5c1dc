# 🚀 VM Deployment - Complete Usage Guide

## ✅ SUCCESS! Your VM is Now Deployed

**Congratulations!** Your VM deployment system is working perfectly. The VM `CCH9APPVMCT-1` has been successfully created with all components:

- ✅ Virtual Network and Subnet
- ✅ Public IP Address  
- ✅ Network Interface
- ✅ Windows Virtual Machine (Standard_D4s_v3)
- ✅ VM Extension with Azure DevOps Agent

## 🎯 How to Use the Form for Future Deployments

### Method 1: HTML Form (Recommended for Beginners)

1. **Open the Form**
   - Open `vm-deployment-form.html` in your web browser
   - The form is pre-filled with working values

2. **Customize Your VM**
   - Change VM Name (e.g., `TestVM`, `DevVM`, `ProdVM`)
   - Adjust VM Count (1-10 VMs)
   - Select VM Size based on your needs
   - Modify other parameters as needed

3. **Deploy Your VM**
   - Click **"⚡ Execute Plan Now"** to see what will be created
   - Click **"⚡ Execute Deploy Now"** to create the VM
   - The form will generate a PowerShell script for you

4. **Run the Generated Script**
   - Copy the PowerShell command from the output
   - Open PowerShell as Administrator
   - Navigate to your Terraform directory
   - Paste and run the command

### Method 2: Quick Deploy Scripts (Fastest)

#### Option A: Batch File (Windows)
```cmd
# Double-click or run from command prompt
Quick-Deploy-VM.bat
```

#### Option B: PowerShell Script
```powershell
# Interactive mode
.\Quick-Deploy-VM.ps1 -Interactive

# Direct execution
.\Quick-Deploy-VM.ps1 -Action "plan" -VmName "MyVM" -VmCount 2

# Deploy immediately
.\Quick-Deploy-VM.ps1 -Action "apply" -VmName "MyVM" -VmCount 2
```

### Method 3: Direct PowerShell (Advanced Users)

```powershell
.\Deploy-VM-Terraform.ps1 `
    -Action "apply" `
    -VmName "CustomVM" `
    -VmCount 2 `
    -ResourceGroupName "MAH9-SUP-CCH9-VMC" `
    -VmSize "Standard_D2s_v3" `
    -AdminUsername "vmadmin" `
    -AdminPassword "YourPassword123!" `
    -AzureDevOpsOrg "https://dev.azure.com/MAHealth" `
    -TeamProject "MAH9-SCP-CCH9GI" `
    -DeploymentGroup "MAH9-SCP-CCH9-DGR-DEV" `
    -AzureDevOpsPat "your-pat-token" `
    -AutoApprove
```

## 📋 Common Deployment Scenarios

### Scenario 1: Single Development VM
```powershell
.\Quick-Deploy-VM.ps1 -Action "apply" -VmName "DevVM" -VmCount 1
```

### Scenario 2: Multiple Test VMs
```powershell
.\Quick-Deploy-VM.ps1 -Action "apply" -VmName "TestVM" -VmCount 3
```

### Scenario 3: Production VM with Custom Size
Use the HTML form with:
- VM Name: `ProdVM`
- VM Count: `1`
- VM Size: `Standard_D8s_v3` (8 vCPUs, 32GB RAM)
- Location: `East US 2`

## 🔍 Verification Steps

After deployment, verify your VM:

### 1. Check Azure Portal
- Go to Azure Portal → Virtual Machines
- Find your VM (e.g., `CCH9APPVMCT-1`)
- Status should show "Running"

### 2. Verify Azure DevOps Agent
- Go to Azure DevOps → Project Settings → Agent pools
- Check Deployment groups → `MAH9-SCP-CCH9-DGR-DEV`
- Your VM should appear as an online agent

### 3. Test Connectivity
```powershell
# Get VM public IP
az vm show -d -g MAH9-SUP-CCH9-VMC -n CCH9APPVMCT-1 --query publicIps -o tsv

# Test RDP connectivity (if enabled)
Test-NetConnection -ComputerName "your-vm-ip" -Port 3389
```

## 🛠️ Customization Options

### VM Sizes Available
| Size | vCPUs | RAM | Use Case |
|------|-------|-----|----------|
| Standard_B2s | 2 | 4GB | Development, Testing |
| Standard_D2s_v3 | 2 | 8GB | Small workloads |
| Standard_D4s_v3 | 4 | 16GB | General purpose (default) |
| Standard_D8s_v3 | 8 | 32GB | High performance |
| Standard_E4s_v3 | 4 | 32GB | Memory intensive |

### Windows Versions
- Windows Server 2019 Datacenter
- Windows Server 2022 Datacenter (default)
- Windows Server 2022 Datacenter Azure Edition

### Azure Regions
- East US
- East US 2 (default)
- West US
- West US 2
- Central US

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Issue 1: "Terraform not found"
**Solution:**
```powershell
# Install Terraform using Chocolatey
choco install terraform

# Or download from https://terraform.io
```

#### Issue 2: "Azure CLI not authenticated"
**Solution:**
```bash
az login
az account set --subscription "your-subscription-id"
```

#### Issue 3: "VM Extension fails"
**Solution:**
- Check Azure DevOps PAT token permissions
- Verify the token has Agent Pools (read, manage) permissions
- Ensure Deployment Groups (read, manage) permissions

#### Issue 4: "Resource Group not found"
**Solution:**
```bash
# Create the resource group
az group create --name "MAH9-SUP-CCH9-VMC" --location "East US 2"
```

#### Issue 5: "PowerShell execution policy"
**Solution:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
```

## 🗑️ Cleanup and Management

### Destroy Single VM
```powershell
.\Quick-Deploy-VM.ps1 -Action "destroy" -VmName "TestVM"
```

### Destroy All Resources
```powershell
terraform destroy -auto-approve
```

### Check Current Resources
```powershell
terraform show
```

### Plan Changes
```powershell
terraform plan
```

## 📊 Cost Management

### Estimated Monthly Costs (East US 2)
| VM Size | Estimated Cost/Month |
|---------|---------------------|
| Standard_B2s | ~$30-50 |
| Standard_D2s_v3 | ~$70-90 |
| Standard_D4s_v3 | ~$140-180 |
| Standard_D8s_v3 | ~$280-350 |

**Note:** Costs include VM, storage, and network. Actual costs may vary.

### Cost Optimization Tips
1. **Stop VMs when not in use**
2. **Use smaller VM sizes for development**
3. **Set up auto-shutdown schedules**
4. **Monitor usage with Azure Cost Management**

## 🔄 Advanced Usage

### Deploy Multiple Different VMs
```powershell
# Deploy development VM
.\Quick-Deploy-VM.ps1 -Action "apply" -VmName "DevVM" -VmCount 1

# Deploy test VMs
.\Quick-Deploy-VM.ps1 -Action "apply" -VmName "TestVM" -VmCount 2

# Deploy production VM
.\Quick-Deploy-VM.ps1 -Action "apply" -VmName "ProdVM" -VmCount 1
```

### Batch Deployment Script
```powershell
# Create multiple environments
$environments = @("Dev", "Test", "Staging")
foreach ($env in $environments) {
    .\Quick-Deploy-VM.ps1 -Action "apply" -VmName "$env-VM" -VmCount 1
    Start-Sleep -Seconds 60  # Wait between deployments
}
```

## 📞 Support and Next Steps

### If You Need Help
1. **Check the troubleshooting section above**
2. **Review Azure Activity Log** for detailed error messages
3. **Check Terraform logs** in the console output
4. **Verify all prerequisites** are installed and configured

### Recommended Next Steps
1. **Set up monitoring** for your VMs
2. **Configure backup policies**
3. **Implement security baselines**
4. **Set up cost alerts**
5. **Create deployment pipelines** for production use

---

## 🎉 Congratulations!

You now have a fully functional VM deployment system with multiple interfaces:
- ✅ HTML Form for easy parameter input
- ✅ Quick deployment scripts for fast execution
- ✅ PowerShell automation for advanced scenarios
- ✅ Working Terraform configuration
- ✅ Azure DevOps agent integration

Your VMs are ready for use! 🚀
