# PowerShell-based Web Server for Terraform VM Deployment
# No Node.js required - uses built-in PowerShell capabilities

param(
    [Parameter(Mandatory=$false)]
    [int]$Port = 8080,
    
    [Parameter(Mandatory=$false)]
    [string]$TerraformPath = "."
)

# Import required modules
Add-Type -AssemblyName System.Web

# Global variables
$script:deployments = @{}
$script:isRunning = $true

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $(
        switch ($Level) {
            "ERROR" { "Red" }
            "WARNING" { "Yellow" }
            "SUCCESS" { "Green" }
            default { "White" }
        }
    )
}

function Get-ContentType {
    param([string]$Extension)
    switch ($Extension.ToLower()) {
        ".html" { return "text/html" }
        ".css" { return "text/css" }
        ".js" { return "application/javascript" }
        ".json" { return "application/json" }
        default { return "text/plain" }
    }
}

function Invoke-TerraformDeployment {
    param(
        [string]$DeploymentId,
        [hashtable]$Parameters
    )
    
    try {
        Write-Log "Starting deployment $DeploymentId" "INFO"
        
        # Update deployment status
        $script:deployments[$DeploymentId].Status = "running"
        $script:deployments[$DeploymentId].Message = "Creating terraform.tfvars..."
        $script:deployments[$DeploymentId].Logs += @{
            Timestamp = Get-Date
            Message = "Starting deployment process"
        }
        
        # Create terraform.tfvars
        $tfvarsContent = @"
vm_name = "$($Parameters.vmName)"
vm_count = $($Parameters.vmCount)
vm_resourcegroup = "$($Parameters.resourceGroup)"
vm_size = "$($Parameters.vmSize)"
vm_sku = "$($Parameters.vmSku)"
vm_location = "$($Parameters.location)"
vm_admin_username = "$($Parameters.adminUsername)"
vm_admin_password = "$($Parameters.adminPassword)"
azure_devops_organization = "$($Parameters.azureDevOpsOrg)"
azure_devops_teamproject = "$($Parameters.teamProject)"
azure_devops_deploymentgroup = "$($Parameters.deploymentGroup)"
azure_devops_pat = "$($Parameters.azureDevOpsPat)"
azure_devops_agentfolder = "$($Parameters.agentFolder)"
"@
        
        $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
        
        $script:deployments[$DeploymentId].Message = "terraform.tfvars created"
        $script:deployments[$DeploymentId].Logs += @{
            Timestamp = Get-Date
            Message = "terraform.tfvars created successfully"
        }
        
        # Initialize Terraform
        $script:deployments[$DeploymentId].Message = "Initializing Terraform..."
        $initResult = & terraform init 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            throw "Terraform init failed: $initResult"
        }
        
        $script:deployments[$DeploymentId].Logs += @{
            Timestamp = Get-Date
            Message = "Terraform initialized successfully"
        }
        
        # Validate configuration
        $script:deployments[$DeploymentId].Message = "Validating configuration..."
        $validateResult = & terraform validate 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            throw "Terraform validation failed: $validateResult"
        }
        
        $script:deployments[$DeploymentId].Logs += @{
            Timestamp = Get-Date
            Message = "Configuration validated successfully"
        }
        
        # Execute the requested action
        switch ($Parameters.action) {
            "plan" {
                $script:deployments[$DeploymentId].Message = "Running Terraform plan..."
                $planResult = & terraform plan -var-file="terraform.tfvars" 2>&1
                
                if ($LASTEXITCODE -ne 0) {
                    throw "Terraform plan failed: $planResult"
                }
                
                $script:deployments[$DeploymentId].Status = "completed"
                $script:deployments[$DeploymentId].Message = "Plan completed successfully"
                $script:deployments[$DeploymentId].Logs += @{
                    Timestamp = Get-Date
                    Message = "Plan output: $planResult"
                }
            }
            
            "apply" {
                $script:deployments[$DeploymentId].Message = "Applying Terraform configuration..."
                $applyResult = & terraform apply -var-file="terraform.tfvars" -auto-approve 2>&1
                
                if ($LASTEXITCODE -ne 0) {
                    throw "Terraform apply failed: $applyResult"
                }
                
                $script:deployments[$DeploymentId].Status = "completed"
                $script:deployments[$DeploymentId].Message = "VM deployed successfully!"
                $script:deployments[$DeploymentId].Logs += @{
                    Timestamp = Get-Date
                    Message = "Apply completed: $applyResult"
                }
            }
            
            "destroy" {
                $script:deployments[$DeploymentId].Message = "Destroying infrastructure..."
                $destroyResult = & terraform destroy -var-file="terraform.tfvars" -auto-approve 2>&1
                
                if ($LASTEXITCODE -ne 0) {
                    throw "Terraform destroy failed: $destroyResult"
                }
                
                $script:deployments[$DeploymentId].Status = "completed"
                $script:deployments[$DeploymentId].Message = "Infrastructure destroyed successfully"
                $script:deployments[$DeploymentId].Logs += @{
                    Timestamp = Get-Date
                    Message = "Destroy completed: $destroyResult"
                }
            }
        }
        
        $script:deployments[$DeploymentId].EndTime = Get-Date
        $duration = ($script:deployments[$DeploymentId].EndTime - $script:deployments[$DeploymentId].StartTime).TotalSeconds
        $script:deployments[$DeploymentId].Duration = [math]::Round($duration)
        
        Write-Log "Deployment $DeploymentId completed successfully" "SUCCESS"
        
    } catch {
        Write-Log "Deployment $DeploymentId failed: $($_.Exception.Message)" "ERROR"
        $script:deployments[$DeploymentId].Status = "failed"
        $script:deployments[$DeploymentId].Message = "Deployment failed: $($_.Exception.Message)"
        $script:deployments[$DeploymentId].Logs += @{
            Timestamp = Get-Date
            Message = "ERROR: $($_.Exception.Message)"
        }
        $script:deployments[$DeploymentId].EndTime = Get-Date
    }
}

function Start-WebServer {
    param([int]$Port)
    
    $listener = New-Object System.Net.HttpListener
    $listener.Prefixes.Add("http://localhost:$Port/")
    $listener.Start()
    
    Write-Log "🚀 PowerShell Web Server started on http://localhost:$Port" "SUCCESS"
    Write-Log "📁 Working directory: $(Get-Location)" "INFO"
    Write-Log "⚡ Ready to deploy VMs automatically!" "SUCCESS"
    Write-Log ""
    Write-Log "Open your browser and go to: http://localhost:$Port" "INFO"
    Write-Log "Press Ctrl+C to stop the server" "INFO"
    Write-Log ""
    
    while ($script:isRunning -and $listener.IsListening) {
        try {
            $context = $listener.GetContext()
            $request = $context.Request
            $response = $context.Response
            
            $url = $request.Url.LocalPath
            $method = $request.HttpMethod
            
            Write-Log "$method $url" "INFO"
            
            # Handle different routes
            switch -Regex ($url) {
                "^/$" {
                    # Serve the main form
                    if (Test-Path "vm-deployment-form-auto.html") {
                        $content = Get-Content "vm-deployment-form-auto.html" -Raw
                        $response.ContentType = "text/html"
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($content)
                        $response.ContentLength64 = $buffer.Length
                        $response.OutputStream.Write($buffer, 0, $buffer.Length)
                    } else {
                        $response.StatusCode = 404
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes("Form not found")
                        $response.OutputStream.Write($buffer, 0, $buffer.Length)
                    }
                }
                
                "^/api/deploy$" {
                    if ($method -eq "POST") {
                        # Handle deployment request
                        $reader = New-Object System.IO.StreamReader($request.InputStream)
                        $body = $reader.ReadToEnd()
                        $data = $body | ConvertFrom-Json
                        
                        $deploymentId = [System.Guid]::NewGuid().ToString()
                        
                        $script:deployments[$deploymentId] = @{
                            Status = "starting"
                            Message = "Deployment initiated"
                            StartTime = Get-Date
                            Action = $data.action
                            VmName = $data.vmName
                            Logs = @()
                        }
                        
                        # Start deployment in background
                        Start-Job -ScriptBlock {
                            param($DeploymentId, $Parameters, $DeploymentsRef)
                            # This would need to be implemented differently for background execution
                        } -ArgumentList $deploymentId, $data, ([ref]$script:deployments)
                        
                        # For now, run synchronously
                        Invoke-TerraformDeployment -DeploymentId $deploymentId -Parameters $data
                        
                        $result = @{
                            success = $true
                            deploymentId = $deploymentId
                            message = "Deployment started"
                            status = "starting"
                        }
                        
                        $response.ContentType = "application/json"
                        $jsonResponse = $result | ConvertTo-Json
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($jsonResponse)
                        $response.ContentLength64 = $buffer.Length
                        $response.OutputStream.Write($buffer, 0, $buffer.Length)
                    }
                }
                
                "^/api/status/(.+)$" {
                    # Handle status request
                    $deploymentId = $Matches[1]
                    
                    if ($script:deployments.ContainsKey($deploymentId)) {
                        $deployment = $script:deployments[$deploymentId]
                        $response.ContentType = "application/json"
                        $jsonResponse = $deployment | ConvertTo-Json -Depth 3
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes($jsonResponse)
                        $response.ContentLength64 = $buffer.Length
                        $response.OutputStream.Write($buffer, 0, $buffer.Length)
                    } else {
                        $response.StatusCode = 404
                        $buffer = [System.Text.Encoding]::UTF8.GetBytes("Deployment not found")
                        $response.OutputStream.Write($buffer, 0, $buffer.Length)
                    }
                }
                
                "^/api/health$" {
                    # Health check
                    $health = @{
                        status = "healthy"
                        timestamp = Get-Date
                        activeDeployments = $script:deployments.Count
                    }
                    
                    $response.ContentType = "application/json"
                    $jsonResponse = $health | ConvertTo-Json
                    $buffer = [System.Text.Encoding]::UTF8.GetBytes($jsonResponse)
                    $response.ContentLength64 = $buffer.Length
                    $response.OutputStream.Write($buffer, 0, $buffer.Length)
                }
                
                default {
                    # 404 for unknown routes
                    $response.StatusCode = 404
                    $buffer = [System.Text.Encoding]::UTF8.GetBytes("Not Found")
                    $response.OutputStream.Write($buffer, 0, $buffer.Length)
                }
            }
            
            $response.Close()
            
        } catch {
            Write-Log "Error handling request: $($_.Exception.Message)" "ERROR"
            try {
                $response.StatusCode = 500
                $response.Close()
            } catch {
                # Ignore errors when closing response
            }
        }
    }
    
    $listener.Stop()
    Write-Log "Web server stopped" "INFO"
}

# Handle Ctrl+C gracefully
$null = Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    $script:isRunning = $false
    Write-Log "Shutting down web server..." "INFO"
}

# Check prerequisites
Write-Log "Checking prerequisites..." "INFO"

if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
    Write-Log "ERROR: Terraform not found in PATH" "ERROR"
    Write-Log "Please install Terraform and ensure it's in your PATH" "ERROR"
    exit 1
}

if (!(Get-Command az -ErrorAction SilentlyContinue)) {
    Write-Log "WARNING: Azure CLI not found" "WARNING"
    Write-Log "Please ensure you're authenticated with Azure" "WARNING"
}

if (!(Test-Path "main.tf")) {
    Write-Log "WARNING: main.tf not found in current directory" "WARNING"
    Write-Log "Current directory: $(Get-Location)" "WARNING"
}

Write-Log "Prerequisites check completed" "INFO"

# Start the web server
try {
    Start-WebServer -Port $Port
} catch {
    Write-Log "Failed to start web server: $($_.Exception.Message)" "ERROR"
    exit 1
}
