/**
 * VM Name Generator for Hospital IT Department
 * Generates standardized VM names based on hospital, department, and user information
 */

class VMNameGenerator {
    constructor() {
        this.maxNameLength = 15; // Azure VM name limit
        this.validVMTypes = {
            'CT': 'Compute',
            'DB': 'Database', 
            'WB': 'Web',
            'AP': 'Application',
            'DC': 'Domain Controller',
            'FS': 'File Server',
            'PS': 'Print Server',
            'TS': 'Terminal Server'
        };
    }

    /**
     * Validates hospital code format
     * @param {string} hospitalCode - 4 character hospital code
     * @returns {object} validation result
     */
    validateHospitalCode(hospitalCode) {
        const code = hospitalCode?.toUpperCase().trim();
        
        if (!code) {
            return { valid: false, error: 'Hospital code is required' };
        }
        
        if (code.length !== 4) {
            return { valid: false, error: 'Hospital code must be exactly 4 characters' };
        }
        
        if (!/^[A-Z0-9]+$/.test(code)) {
            return { valid: false, error: 'Hospital code must contain only letters and numbers' };
        }
        
        return { valid: true, code: code };
    }

    /**
     * Validates department code format
     * @param {string} departmentCode - 4 character department code
     * @returns {object} validation result
     */
    validateDepartmentCode(departmentCode) {
        const code = departmentCode?.toUpperCase().trim();
        
        if (!code) {
            return { valid: false, error: 'Department code is required' };
        }
        
        if (code.length !== 4) {
            return { valid: false, error: 'Department code must be exactly 4 characters' };
        }
        
        if (!/^[A-Z]+$/.test(code)) {
            return { valid: false, error: 'Department code must contain only letters' };
        }
        
        return { valid: true, code: code };
    }

    /**
     * Validates user name and generates initials
     * @param {string} firstName - User's first name
     * @param {string} lastName - User's last name
     * @returns {object} validation result with initials
     */
    validateUserName(firstName, lastName) {
        const first = firstName?.trim();
        const last = lastName?.trim();
        
        if (!first || !last) {
            return { valid: false, error: 'Both first name and last name are required' };
        }
        
        if (first.length < 2 || last.length < 2) {
            return { valid: false, error: 'Names must be at least 2 characters long' };
        }
        
        if (!/^[A-Za-z]+$/.test(first) || !/^[A-Za-z]+$/.test(last)) {
            return { valid: false, error: 'Names must contain only letters' };
        }
        
        const initials = (first.charAt(0) + last.charAt(0)).toUpperCase();
        
        return { 
            valid: true, 
            initials: initials,
            firstName: first,
            lastName: last
        };
    }

    /**
     * Validates VM type
     * @param {string} vmType - VM type code
     * @returns {object} validation result
     */
    validateVMType(vmType) {
        const type = vmType?.toUpperCase().trim();
        
        if (!type) {
            return { valid: false, error: 'VM type is required' };
        }
        
        if (!this.validVMTypes[type]) {
            return { 
                valid: false, 
                error: `Invalid VM type. Valid types: ${Object.keys(this.validVMTypes).join(', ')}` 
            };
        }
        
        return { 
            valid: true, 
            type: type,
            description: this.validVMTypes[type]
        };
    }

    /**
     * Generates VM name based on input parameters
     * @param {object} params - Input parameters
     * @returns {object} Generation result
     */
    generateVMName(params) {
        const {
            hospitalCode,
            departmentCode,
            firstName,
            lastName,
            vmType = 'CT',
            vmCount = 1
        } = params;

        // Validate all inputs
        const hospitalValidation = this.validateHospitalCode(hospitalCode);
        if (!hospitalValidation.valid) {
            return { success: false, error: hospitalValidation.error };
        }

        const departmentValidation = this.validateDepartmentCode(departmentCode);
        if (!departmentValidation.valid) {
            return { success: false, error: departmentValidation.error };
        }

        const userValidation = this.validateUserName(firstName, lastName);
        if (!userValidation.valid) {
            return { success: false, error: userValidation.error };
        }

        const typeValidation = this.validateVMType(vmType);
        if (!typeValidation.valid) {
            return { success: false, error: typeValidation.error };
        }

        // Generate base VM name
        const baseName = `${hospitalValidation.code}${departmentValidation.code}${userValidation.initials}VM${typeValidation.type}`;
        
        // Check length constraint
        if (baseName.length > this.maxNameLength) {
            return { 
                success: false, 
                error: `Generated VM name '${baseName}' exceeds maximum length of ${this.maxNameLength} characters` 
            };
        }

        // Generate names for multiple VMs if requested
        const vmNames = [];
        for (let i = 1; i <= vmCount; i++) {
            if (vmCount === 1) {
                vmNames.push(baseName);
            } else {
                const numberedName = `${baseName}${i.toString().padStart(2, '0')}`;
                if (numberedName.length > this.maxNameLength) {
                    return { 
                        success: false, 
                        error: `Generated VM name '${numberedName}' exceeds maximum length of ${this.maxNameLength} characters` 
                    };
                }
                vmNames.push(numberedName);
            }
        }

        return {
            success: true,
            vmNames: vmNames,
            baseName: baseName,
            metadata: {
                hospitalCode: hospitalValidation.code,
                departmentCode: departmentValidation.code,
                userInitials: userValidation.initials,
                vmType: typeValidation.type,
                vmTypeDescription: typeValidation.description,
                firstName: userValidation.firstName,
                lastName: userValidation.lastName,
                vmCount: vmCount
            }
        };
    }

    /**
     * Generates Terraform variables for the VM deployment
     * @param {object} params - Input parameters
     * @returns {object} Terraform variables
     */
    generateTerraformVars(params) {
        const nameResult = this.generateVMName(params);
        
        if (!nameResult.success) {
            return { success: false, error: nameResult.error };
        }

        const primaryVMName = nameResult.vmNames[0];
        
        return {
            success: true,
            terraformVars: {
                vm_name: primaryVMName,
                vm_count: params.vmCount || 1,
                vm_resourcegroup: params.resourceGroup || "MAH9-SUP-CCH9-VMC",
                vm_size: params.vmSize || "Standard_D4s_v3",
                vm_location: params.location || "East US 2",
                vm_admin_username: params.adminUsername || "maoperator",
                vm_admin_password: params.adminPassword || "M1t1g@t0r2025",
                azure_devops_organization: "https://dev.azure.com/MAHealth",
                azure_devops_teamproject: params.teamProject || "MAH9-SCP-CCH9GI",
                azure_devops_deploymentgroup: params.deploymentGroup || "MAH9-SCP-CCH9-DGR-DEV",
                azure_devops_pat: params.azureDevOpsPat,
                azure_devops_agentfolder: "c:/Agent"
            },
            vmNames: nameResult.vmNames,
            metadata: nameResult.metadata
        };
    }

    /**
     * Generates deployment metadata for tracking
     * @param {object} params - Input parameters
     * @returns {object} Deployment metadata
     */
    generateDeploymentMetadata(params) {
        const nameResult = this.generateVMName(params);
        
        if (!nameResult.success) {
            return { success: false, error: nameResult.error };
        }

        return {
            success: true,
            metadata: {
                deploymentId: this.generateDeploymentId(),
                requestDate: new Date().toISOString(),
                requestedBy: `${nameResult.metadata.firstName} ${nameResult.metadata.lastName}`,
                hospitalCode: nameResult.metadata.hospitalCode,
                departmentCode: nameResult.metadata.departmentCode,
                vmNames: nameResult.vmNames,
                vmType: nameResult.metadata.vmTypeDescription,
                vmCount: nameResult.metadata.vmCount,
                status: 'pending'
            }
        };
    }

    /**
     * Generates unique deployment ID
     * @returns {string} Deployment ID
     */
    generateDeploymentId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `DEP-${timestamp}-${random}`.toUpperCase();
    }
}

// Export for Node.js environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VMNameGenerator;
}

// Example usage and testing
if (typeof window !== 'undefined') {
    // Browser environment - attach to window for global access
    window.VMNameGenerator = VMNameGenerator;
    
    // Example usage
    const generator = new VMNameGenerator();
    
    const example = generator.generateVMName({
        hospitalCode: 'CCH9',
        departmentCode: 'SUPP',
        firstName: 'John',
        lastName: 'Doe',
        vmType: 'CT',
        vmCount: 2
    });
    
    console.log('Example VM Name Generation:', example);
}
