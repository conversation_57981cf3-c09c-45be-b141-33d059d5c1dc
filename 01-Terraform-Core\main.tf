## <https://www.terraform.io/docs/providers/azurerm/index.html>

locals {
  ps01="Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072"
  ps02="iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))"
  ps03="refreshenv; Import-Module $env:ChocolateyInstall\\helpers\\chocolateyProfile.psm1"
  pscfg = "${local.ps01}; ${local.ps02}; ${local.ps03}"
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=2.46.0"
    }
  }
}

# Configure the Microsoft Azure Provider
provider "azurerm" {
  features {}
}

## <https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/data-sources/resource_group>
data "azurerm_resource_group" "rg" {
  name = var.vm_resourcegroup
}

## <https://www.terraform.io/docs/providers/azurerm/r/virtual_network.html>
resource "azurerm_virtual_network" "vnet" {
  name                = "${var.vm_name}-vNet"
  address_space       = ["10.0.0.0/16"]
  location            = "${data.azurerm_resource_group.rg.location}"
  resource_group_name = "${data.azurerm_resource_group.rg.name}"
}

## <https://www.terraform.io/docs/providers/azurerm/r/subnet.html> 
resource "azurerm_subnet" "subnet" {
  name                 = "${var.vm_name}-subnet"
  resource_group_name  = "${data.azurerm_resource_group.rg.name}"
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes       = ["********/24"]
}

resource "azurerm_public_ip" "example" {
 count                 = "${var.vm_count}"
  name                = "PIP-${var.vm_name}-${count.index + 1}"
  resource_group_name = "${data.azurerm_resource_group.rg.name}"
  location            = "${data.azurerm_resource_group.rg.location}"
  allocation_method   = "Dynamic"
  domain_name_label  = "${lower(var.vm_name)}-${count.index + 1}"
}

## <https://www.terraform.io/docs/providers/azurerm/r/network_interface.html>
resource "azurerm_network_interface" "nic" {
  count                = "${var.vm_count}"
  name                = "${var.vm_name}-nic${count.index + 1}"
  location            = "${data.azurerm_resource_group.rg.location}"
  resource_group_name = "${data.azurerm_resource_group.rg.name}"

  ip_configuration {
    name                          = "internal"
    subnet_id                     = azurerm_subnet.subnet.id
    private_ip_address_allocation = "Dynamic"
    public_ip_address_id = "${element(azurerm_public_ip.example.*.id, count.index)}"
  }
}


## <https://www.terraform.io/docs/providers/azurerm/r/windows_virtual_machine.html>
resource "azurerm_windows_virtual_machine" "vm" {
  count                 = "${var.vm_count}"
 name                  = "${var.vm_name}-${count.index + 1}"
  resource_group_name = "${data.azurerm_resource_group.rg.name}"
  location            = "${data.azurerm_resource_group.rg.location}"
  size                = var.vm_size
  admin_username      = var.vm_admin_username
  admin_password      = var.vm_admin_password
  network_interface_ids = ["${element(azurerm_network_interface.nic.*.id, count.index)}"]

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
  }

  source_image_reference {
    publisher = var.vm_publisher
    offer     = var.vm_offer
    sku       = var.vm_sku
    version   = "latest"
  }
}

resource "azurerm_virtual_machine_extension" "customize" {
    count                 = var.vm_count
    name                 = "customize-${count.index + 1}"
    virtual_machine_id   = azurerm_windows_virtual_machine.vm[count.index].id
    publisher            = "Microsoft.Compute"
    type                 = "CustomScriptExtension"
    type_handler_version = "1.10"

    settings = jsonencode({
        "commandToExecute" = "powershell.exe -ExecutionPolicy Unrestricted -Command \"${local.pscfg}; Start-Sleep -Seconds 60; $env:Path = [System.Environment]::GetEnvironmentVariable('Path','Machine') + ';' + [System.Environment]::GetEnvironmentVariable('Path','User'); choco install azure-pipelines-agent --params '/Directory:${var.azure_devops_agentfolder} /Token:${var.azure_devops_pat} /ProjectName:${var.azure_devops_teamproject} /DeploymentGroup:true /DeploymentGroupName:${var.azure_devops_deploymentgroup} /DeploymentGroupTags:${azurerm_windows_virtual_machine.vm[count.index].name} /LogonAccount:${var.vm_admin_username} /LogonPassword:${var.vm_admin_password} /Url:${var.azure_devops_organization}' --confirm --force\""
    })

    depends_on = [azurerm_windows_virtual_machine.vm]
}
