@echo off
cls
echo ========================================
echo    Azure VM Deployment - Main Launcher
echo ========================================
echo.

echo Available Deployment Methods:
echo.
echo 1. HTML Form (User-Friendly)
echo 2. Interactive Batch Script
echo 3. PowerShell Automation
echo 4. Web Server Interface
echo 5. Exit
echo.

set /p choice="Select deployment method (1-5): "

if "%choice%"=="1" goto html_form
if "%choice%"=="2" goto batch_script
if "%choice%"=="3" goto powershell_script
if "%choice%"=="4" goto web_server
if "%choice%"=="5" goto exit

echo Invalid choice. Please select 1-5.
pause
goto start

:html_form
echo.
echo Opening HTML Form...
echo Available forms:
echo 1. Working Form (Recommended)
echo 2. Auto-Deploy Form
echo 3. Basic Form
echo 4. Automated Form
echo.
set /p form_choice="Select form (1-4): "

if "%form_choice%"=="1" start "" "02-HTML-Forms\vm-deployment-working.html"
if "%form_choice%"=="2" start "" "02-HTML-Forms\vm-deployment-form-auto.html"
if "%form_choice%"=="3" start "" "02-HTML-Forms\vm-deployment-form.html"
if "%form_choice%"=="4" start "" "02-HTML-Forms\vm-deployment-automated.html"

echo Form opened in your default browser.
echo Fill the form and follow the instructions.
pause
goto exit

:batch_script
echo.
echo Available Batch Scripts:
echo 1. Simple Interactive Deploy (Recommended)
echo 2. Quick Deploy
echo 3. Auto Deploy VM
echo.
set /p batch_choice="Select script (1-3): "

if "%batch_choice%"=="1" (
    echo Starting Simple Interactive Deploy...
    cd /d "%~dp0"
    call "04-Batch-Files\Deploy-VM-Simple.bat"
)
if "%batch_choice%"=="2" (
    echo Starting Quick Deploy...
    cd /d "%~dp0"
    call "04-Batch-Files\Quick-Deploy-VM.bat"
)
if "%batch_choice%"=="3" (
    echo Starting Auto Deploy...
    cd /d "%~dp0"
    call "04-Batch-Files\Auto-Deploy-VM.bat"
)
goto exit

:powershell_script
echo.
echo Available PowerShell Scripts:
echo 1. Interactive Deploy (Recommended)
echo 2. Simple Auto Deploy
echo 3. Automated Deployment Engine
echo.
set /p ps_choice="Select script (1-3): "

if "%ps_choice%"=="1" (
    echo Starting Interactive Deploy...
    powershell.exe -ExecutionPolicy Bypass -File "03-PowerShell-Scripts\Deploy-VM-Interactive.ps1"
)
if "%ps_choice%"=="2" (
    echo Starting Simple Auto Deploy...
    powershell.exe -ExecutionPolicy Bypass -File "03-PowerShell-Scripts\Simple-Auto-Deploy.ps1"
)
if "%ps_choice%"=="3" (
    echo Starting Automated Deployment Engine...
    powershell.exe -ExecutionPolicy Bypass -File "03-PowerShell-Scripts\Start-AutomatedDeployment.ps1"
)
goto exit

:web_server
echo.
echo Available Web Servers:
echo 1. Python Web App (Full Featured)
echo 2. Node.js Web Server
echo 3. PowerShell Web Server
echo.
set /p web_choice="Select server (1-3): "

if "%web_choice%"=="1" (
    echo Starting Python Web App...
    echo Note: Requires Python to be installed
    cd /d "%~dp0\05-Web-Servers"
    python terraform-web-app.py
)
if "%web_choice%"=="2" (
    echo Starting Node.js Web Server...
    echo Note: Requires Node.js to be installed
    cd /d "%~dp0\05-Web-Servers"
    node terraform-web-server.js
)
if "%web_choice%"=="3" (
    echo Starting PowerShell Web Server...
    powershell.exe -ExecutionPolicy Bypass -File "03-PowerShell-Scripts\Start-TerraformWebServer.ps1"
)
goto exit

:exit
echo.
echo ========================================
echo    Deployment Launcher Closed
echo ========================================
pause
