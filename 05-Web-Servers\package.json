{"name": "terraform-vm-deployment", "version": "1.0.0", "description": "Automated VM deployment using Terraform with web interface", "main": "terraform-web-server.js", "scripts": {"start": "node terraform-web-server.js", "dev": "nodemon terraform-web-server.js", "install-deps": "npm install", "setup": "npm install && echo Setup complete! Run 'npm start' to begin."}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["terraform", "azure", "vm", "deployment", "automation"], "author": "VM Deployment Automation", "license": "MIT"}