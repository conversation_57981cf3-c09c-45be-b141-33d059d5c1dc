# VM Deployment Automation - Quick Start Guide

## 🚀 Getting Started in 15 Minutes

This guide will help you deploy your first VM using the automated deployment solution. Follow these steps to get up and running quickly.

## ✅ Prerequisites Checklist

Before starting, ensure you have:

- [ ] **Azure Subscription** with Contributor access
- [ ] **Azure CLI** installed and authenticated (`az login`)
- [ ] **Terraform** installed (v1.5.7+)
- [ ] **PowerShell** 7.0+ installed
- [ ] **Git** for cloning the repository
- [ ] **Azure DevOps** organization (for agent deployment)

### Quick Prerequisites Check

```powershell
# Check Azure CLI
az --version
az account show

# Check Terraform
terraform --version

# Check PowerShell
$PSVersionTable.PSVersion

# Check Git
git --version
```

## 🎯 Method 1: HTML Form (Easiest - 5 Minutes)

### Step 1: Open the Form
1. Navigate to your project directory
2. Open `vm-deployment-form.html` in your web browser
3. The form will load with default values

### Step 2: Configure Parameters
Fill in the required fields:

| Field | Example Value | Description |
|-------|---------------|-------------|
| **VM Name** | `MyTestVM` | Base name for your VM(s) |
| **VM Count** | `1` | Number of VMs to create |
| **Resource Group** | `test-rg` | Existing Azure resource group |
| **Admin Username** | `vmadmin` | VM administrator account |
| **Admin Password** | `SecurePass123!` | Strong password (12+ chars) |
| **Azure DevOps Org** | `https://dev.azure.com/yourorg` | Your DevOps organization |
| **Team Project** | `MyProject` | DevOps project name |
| **Deployment Group** | `test-group` | DevOps deployment group |
| **PAT Token** | `your-pat-token` | Personal Access Token |

### Step 3: Generate Configuration
1. Click **"📋 Generate terraform.tfvars"**
2. The file will be automatically downloaded
3. Save it to your Terraform directory

### Step 4: Deploy
1. Click **"🔍 Plan Deployment"** to see what will be created
2. Review the PowerShell command generated
3. Copy and run the command in PowerShell
4. If satisfied with the plan, click **"🚀 Deploy Infrastructure"**

## ⚡ Method 2: PowerShell Direct (Power Users - 3 Minutes)

### Quick Deployment Command

```powershell
# Navigate to your Terraform directory
cd "C:\path\to\your\terraform\directory"

# Run the deployment script
.\Deploy-VM-Terraform.ps1 `
    -Action "plan" `
    -VmName "QuickTestVM" `
    -VmCount 1 `
    -ResourceGroupName "your-resource-group" `
    -AdminUsername "vmadmin" `
    -AdminPassword "YourSecurePassword123!" `
    -AzureDevOpsOrg "https://dev.azure.com/yourorg" `
    -TeamProject "YourProject" `
    -DeploymentGroup "test-group" `
    -AzureDevOpsPat "your-pat-token"
```

### If Plan Looks Good, Apply:

```powershell
# Change action to apply
.\Deploy-VM-Terraform.ps1 `
    -Action "apply" `
    -VmName "QuickTestVM" `
    -VmCount 1 `
    -ResourceGroupName "your-resource-group" `
    -AdminUsername "vmadmin" `
    -AdminPassword "YourSecurePassword123!" `
    -AzureDevOpsOrg "https://dev.azure.com/yourorg" `
    -TeamProject "YourProject" `
    -DeploymentGroup "test-group" `
    -AzureDevOpsPat "your-pat-token" `
    -AutoApprove
```

## 🔧 Common First-Time Setup

### 1. Create Resource Group (if needed)

```bash
# Create a new resource group
az group create --name "vm-test-rg" --location "East US 2"
```

### 2. Get Azure DevOps PAT Token

1. Go to Azure DevOps → User Settings → Personal Access Tokens
2. Click **"New Token"**
3. Set scope to:
   - **Agent Pools**: Read & manage
   - **Deployment Groups**: Read & manage
4. Copy the token (you won't see it again!)

### 3. Verify Terraform Configuration

```powershell
# Initialize Terraform (first time only)
terraform init

# Validate configuration
terraform validate

# Check what will be created
terraform plan
```

## 🎯 Your First Deployment Walkthrough

### Scenario: Deploy a Test VM

Let's deploy a single test VM with these specifications:
- **Name**: TestVM-001
- **Size**: Standard_B2s (cost-effective for testing)
- **OS**: Windows Server 2022
- **Location**: East US 2

### Step-by-Step Process

1. **Prepare Environment**
   ```powershell
   # Set your working directory
   cd "D:\Maspects\MAHealth\Terraform\Scope_Demo-Environment"
   
   # Verify Azure login
   az account show
   ```

2. **Use the HTML Form**
   - Open `vm-deployment-form.html`
   - Set VM Name: `TestVM`
   - Set VM Count: `1`
   - Set VM Size: `Standard_B2s`
   - Fill in your Azure DevOps details
   - Click "Generate terraform.tfvars"

3. **Plan the Deployment**
   ```powershell
   .\Deploy-VM-Terraform.ps1 -Action "plan" -VmName "TestVM" -VmCount 1 -ResourceGroupName "MAH9-SUP-CCH9-VMC" -VmSize "Standard_B2s" -AdminUsername "vmadmin" -AdminPassword "TestPass123!" -AzureDevOpsOrg "https://dev.azure.com/MAHealth" -TeamProject "MAH9-SCP-CCH9GI" -DeploymentGroup "MAH9-SCP-CCH9-DGR-DEV" -AzureDevOpsPat "your-token-here"
   ```

4. **Review the Plan**
   - Check the resources that will be created
   - Verify VM specifications
   - Confirm network configuration

5. **Deploy the VM**
   ```powershell
   # If plan looks good, apply the changes
   .\Deploy-VM-Terraform.ps1 -Action "apply" -VmName "TestVM" -VmCount 1 -ResourceGroupName "MAH9-SUP-CCH9-VMC" -VmSize "Standard_B2s" -AdminUsername "vmadmin" -AdminPassword "TestPass123!" -AzureDevOpsOrg "https://dev.azure.com/MAHealth" -TeamProject "MAH9-SCP-CCH9GI" -DeploymentGroup "MAH9-SCP-CCH9-DGR-DEV" -AzureDevOpsPat "your-token-here" -AutoApprove
   ```

6. **Monitor Progress**
   - Watch the Terraform output
   - Check Azure portal for resource creation
   - Verify Azure DevOps agent registration

## 🔍 Verification Steps

### 1. Check VM in Azure Portal
- Navigate to Azure Portal → Virtual Machines
- Find your VM (e.g., TestVM-1)
- Verify it's running and properly configured

### 2. Verify Azure DevOps Agent
- Go to Azure DevOps → Project Settings → Agent pools
- Check Deployment groups
- Confirm your VM appears as an agent

### 3. Test VM Connectivity
```powershell
# Test RDP connectivity (if enabled)
Test-NetConnection -ComputerName "your-vm-public-ip" -Port 3389

# Or use Azure Bastion/Serial Console
```

## 🚨 Troubleshooting Quick Fixes

### Issue: Terraform Init Fails
```powershell
# Clear Terraform cache and reinitialize
Remove-Item .terraform -Recurse -Force
terraform init
```

### Issue: Azure CLI Not Authenticated
```bash
# Re-authenticate
az login
az account set --subscription "your-subscription-id"
```

### Issue: VM Extension Fails
- Check the VM extension logs in Azure Portal
- Verify Azure DevOps PAT token permissions
- Ensure Chocolatey installation completed

### Issue: PowerShell Execution Policy
```powershell
# Set execution policy for current session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
```

## 📊 Expected Timeline

| Task | Duration | Notes |
|------|----------|-------|
| **Prerequisites Setup** | 10-15 min | One-time setup |
| **Form Configuration** | 2-3 min | Parameter input |
| **Terraform Plan** | 1-2 min | Quick validation |
| **VM Deployment** | 10-15 min | Includes extensions |
| **Agent Registration** | 3-5 min | DevOps agent setup |
| **Total First Deployment** | 25-40 min | Including setup |

## 🎯 Success Criteria

Your deployment is successful when:

- [ ] VM appears in Azure Portal as "Running"
- [ ] VM extension shows "Succeeded" status
- [ ] Azure DevOps agent appears in deployment group
- [ ] Agent status shows "Online"
- [ ] You can connect to the VM (RDP/Bastion)

## 🚀 Next Steps

After your first successful deployment:

1. **Try Multiple VMs**: Set VmCount to 2 or 3
2. **Test Different Sizes**: Try Standard_D2s_v3 or Standard_D4s_v3
3. **Set up Power Automate**: For workflow automation
4. **Configure Azure DevOps Pipeline**: For production deployments
5. **Implement Monitoring**: Set up alerts and dashboards

## 📞 Need Help?

If you encounter issues:

1. **Check the logs**: PowerShell script creates detailed logs
2. **Review Azure Activity Log**: For resource creation issues
3. **Verify prerequisites**: Ensure all tools are properly installed
4. **Check permissions**: Verify Azure and DevOps access rights

## 🔄 Clean Up Test Resources

When you're done testing:

```powershell
# Destroy the test VM
.\Deploy-VM-Terraform.ps1 -Action "destroy" -VmName "TestVM" -VmCount 1 -ResourceGroupName "MAH9-SUP-CCH9-VMC" -AutoApprove

# Or delete the entire resource group (if it was created for testing)
az group delete --name "vm-test-rg" --yes --no-wait
```

---

**Congratulations!** 🎉 You've successfully deployed your first VM using the automated deployment solution. You're now ready to explore the advanced features and set up production deployments.
