{"definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"subscriptionId": {"type": "string", "defaultValue": "41ae4571-862f-4682-ac9d-c8e39c9ce301"}, "devOpsOrganization": {"type": "string", "defaultValue": "https://dev.azure.com/MAHealth"}, "devOpsProject": {"type": "string", "defaultValue": "MAH9-SCP-CCH9GI"}, "pipelineId": {"type": "string", "defaultValue": "terraform-vm-deployment"}, "teamsChannelId": {"type": "string", "defaultValue": "your-teams-channel-id"}}, "triggers": {"When_a_new_response_is_submitted": {"type": "ApiConnectionWebhook", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['microsoftforms']['connectionId']"}}, "path": "/formapi/api/forms/@{encodeURIComponent('YOUR_FORM_ID')}/responses", "queries": {"api-version": "1.0"}}}}, "actions": {"Get_response_details": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['microsoftforms']['connectionId']"}}, "method": "get", "path": "/formapi/api/forms/@{encodeURIComponent('YOUR_FORM_ID')}/responses/@{encodeURIComponent(triggerBody()?['resourceData']?['responseId'])}", "queries": {"api-version": "1.0"}}, "runAfter": {}}, "Initialize_Variables": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "deploymentId", "type": "string", "value": "@{concat('VM-', formatDateTime(utcNow(), 'yyyyMMddHHmmss'), '-', substring(guid(), 0, 8))}"}, {"name": "hospitalCode", "type": "string", "value": "@{toUpper(trim(body('Get_response_details')?['r1c1d1']))}"}, {"name": "departmentCode", "type": "string", "value": "@{toUpper(trim(body('Get_response_details')?['r2c1d1']))}"}, {"name": "firstName", "type": "string", "value": "@{trim(coalesce(body('Get_response_details')?['r3c1d1'], ''))}"}, {"name": "lastName", "type": "string", "value": "@{trim(coalesce(body('Get_response_details')?['r3c2d1'], ''))}"}]}, "runAfter": {"Get_response_details": ["Succeeded"]}}, "Validate_Required_Fields": {"type": "Compose", "inputs": {"hospitalCodeValid": "@{and(not(empty(variables('hospitalCode'))), lessOrEquals(length(variables('hospitalCode')), 4))}", "departmentCodeValid": "@{and(not(empty(variables('departmentCode'))), lessOrEquals(length(variables('departmentCode')), 4))}", "hasUserNames": "@{and(not(empty(variables('firstName'))), not(empty(variables('lastName'))))}"}, "runAfter": {"Initialize_Variables": ["Succeeded"]}}, "Check_Validation": {"type": "Condition", "expression": {"and": [{"equals": ["@outputs('Validate_Required_Fields')['hospitalCodeValid']", true]}, {"equals": ["@outputs('Validate_Required_Fields')['departmentCodeValid']", true]}]}, "actions": {"Generate_VM_Name": {"type": "Compose", "inputs": {"vmName": "@{if(outputs('Validate_Required_Fields')['hasUserNames'], concat(variables('hospitalCode'), variables('departmentCode'), toUpper(substring(variables('firstName'), 0, 1)), toUpper(substring(variables('lastName'), 0, 1)), 'VMCT'), concat(variables('hospitalCode'), variables('departmentCode'), 'VMCT'))}"}}, "Send_Teams_Notification": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "method": "post", "path": "/flowbot/actions/adaptivecard/recipienttype/channel", "queries": {"recipient": "@parameters('teamsChannelId')"}, "body": {"messageBody": {"type": "AdaptiveCard", "body": [{"type": "TextBlock", "text": "🏥 New VM Deployment Request", "weight": "Bolder", "size": "Medium", "color": "Accent"}, {"type": "FactSet", "facts": [{"title": "Deployment ID:", "value": "@{variables('deploymentId')}"}, {"title": "Hospital:", "value": "@{variables('hospitalCode')}"}, {"title": "Department:", "value": "@{variables('departmentCode')}"}, {"title": "Requested By:", "value": "@{if(outputs('Validate_Required_Fields')['hasUserNames'], concat(variables('firstName'), ' ', variables('lastName')), 'System Request')}"}, {"title": "VM Name:", "value": "@{outputs('Generate_VM_Name')['vmName']}"}, {"title": "Status:", "value": "🚀 Starting Deployment..."}]}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.2"}}}, "runAfter": {"Generate_VM_Name": ["Succeeded"]}}, "Trigger_DevOps_Pipeline": {"type": "Http", "inputs": {"method": "POST", "uri": "@{concat(parameters('devOpsOrganization'), '/', parameters('devOpsProject'), '/_apis/pipelines/', parameters('pipelineId'), '/runs?api-version=6.0-preview.1')}", "headers": {"Authorization": "Basic @{base64(concat(':', 'YOUR_AZURE_DEVOPS_PAT'))}", "Content-Type": "application/json"}, "body": {"templateParameters": {"vmName": "@{outputs('Generate_VM_Name')['vmName']}", "hospitalCode": "@{variables('hospitalCode')}", "departmentCode": "@{variables('departmentCode')}", "firstName": "@{variables('firstName')}", "lastName": "@{variables('lastName')}", "deploymentId": "@{variables('deploymentId')}", "requestedBy": "@{if(outputs('Validate_Required_Fields')['hasUserNames'], concat(variables('firstName'), ' ', variables('lastName')), 'System Request')}"}}}, "runAfter": {"Send_Teams_Notification": ["Succeeded"]}}}, "else": {"actions": {"Send_Error_Notification": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "method": "post", "path": "/flowbot/actions/adaptivecard/recipienttype/channel", "queries": {"recipient": "@parameters('teamsChannelId')"}, "body": {"messageBody": {"type": "AdaptiveCard", "body": [{"type": "TextBlock", "text": "❌ VM Request Validation Failed", "weight": "Bolder", "size": "Medium", "color": "Attention"}, {"type": "FactSet", "facts": [{"title": "Hospital Code:", "value": "@{concat(variables('hospitalCode'), if(outputs('Validate_Required_Fields')['hospitalCodeValid'], ' ✅', ' ❌ (Required, max 4 chars)')}"}, {"title": "Department Code:", "value": "@{concat(variables('departmentCode'), if(outputs('Validate_Required_Fields')['departmentCodeValid'], ' ✅', ' ❌ (Required, max 4 chars)'))"}]}, {"type": "TextBlock", "text": "Please correct the form data and resubmit.", "wrap": true}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.2"}}}}}}, "runAfter": {"Validate_Required_Fields": ["Succeeded"]}}}, "outputs": {}}, "parameters": {"$connections": {"value": {"microsoftforms": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/microsoftforms", "connectionName": "microsoftforms", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/microsoftforms"}, "teams": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/teams", "connectionName": "teams", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/teams"}}}}}