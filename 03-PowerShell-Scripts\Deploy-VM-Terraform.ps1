# Enhanced PowerShell script for automated Terraform VM deployment
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("plan", "apply", "destroy")]
    [string]$Action,
    
    [Parameter(Mandatory=$true)]
    [string]$VmName,
    
    [Parameter(Mandatory=$false)]
    [int]$VmCount = 1,
    
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$false)]
    [string]$VmSize = "Standard_D4s_v3",
    
    [Parameter(Mandatory=$false)]
    [string]$VmSku = "2022-datacenter-azure-edition",
    
    [Parameter(Mandatory=$false)]
    [string]$Location = "East US 2",
    
    [Parameter(Mandatory=$true)]
    [string]$AdminUsername,
    
    [Parameter(Mandatory=$true)]
    [string]$AdminPassword,
    
    [Parameter(Mandatory=$true)]
    [string]$AzureDevOpsOrg,
    
    [Parameter(Mandatory=$true)]
    [string]$TeamProject,
    
    [Parameter(Mandatory=$true)]
    [string]$DeploymentGroup,
    
    [Parameter(Mandatory=$true)]
    [string]$AzureDevOpsPat,
    
    [Parameter(Mandatory=$false)]
    [string]$AgentFolder = "c:/Agent",
    
    [Parameter(Mandatory=$false)]
    [string]$TerraformPath = ".",
    
    [Parameter(Mandatory=$false)]
    [switch]$AutoApprove = $false,
    
    [Parameter(Mandatory=$false)]
    [string]$LogFile = "terraform-deployment.log"
)

# Function to write logs
function Write-Log {
    param(
        [string]$Message, 
        [string]$Level = "INFO",
        [string]$LogPath = $LogFile
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Output $logEntry
    Add-Content -Path $LogPath -Value $logEntry
}

# Function to validate prerequisites
function Test-Prerequisites {
    Write-Log "Checking prerequisites..."
    
    # Check if Terraform is installed
    try {
        $terraformVersion = terraform --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Terraform found: $($terraformVersion.Split("`n")[0])"
        } else {
            throw "Terraform not found"
        }
    } catch {
        Write-Log "Terraform not found. Please install Terraform first." "ERROR"
        return $false
    }
    
    # Check if Azure CLI is installed and logged in
    try {
        $azAccount = az account show 2>$null | ConvertFrom-Json
        if ($azAccount) {
            Write-Log "Azure CLI authenticated as: $($azAccount.user.name)"
        } else {
            throw "Not logged in to Azure"
        }
    } catch {
        Write-Log "Azure CLI not authenticated. Please run 'az login' first." "ERROR"
        return $false
    }
    
    # Check if Terraform directory exists
    if (!(Test-Path $TerraformPath)) {
        Write-Log "Terraform directory not found: $TerraformPath" "ERROR"
        return $false
    }
    
    return $true
}

# Function to create terraform.tfvars
function New-TerraformVars {
    Write-Log "Creating terraform.tfvars file..."
    
    $tfvarsContent = @"
# Generated terraform.tfvars file
# Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

vm_name = "$VmName"
vm_count = $VmCount
vm_resourcegroup = "$ResourceGroupName"
vm_size = "$VmSize"
vm_sku = "$VmSku"
vm_location = "$Location"
vm_admin_username = "$AdminUsername"
vm_admin_password = "$AdminPassword"
azure_devops_organization = "$AzureDevOpsOrg"
azure_devops_teamproject = "$TeamProject"
azure_devops_deploymentgroup = "$DeploymentGroup"
azure_devops_pat = "$AzureDevOpsPat"
azure_devops_agentfolder = "$AgentFolder"
"@
    
    $tfvarsPath = Join-Path $TerraformPath "terraform.tfvars"
    $tfvarsContent | Out-File -FilePath $tfvarsPath -Encoding UTF8
    Write-Log "Created terraform.tfvars at: $tfvarsPath"
    
    return $tfvarsPath
}

# Function to run Terraform commands
function Invoke-TerraformCommand {
    param(
        [string]$Command,
        [string[]]$Arguments = @(),
        [int]$TimeoutMinutes = 30
    )
    
    $fullCommand = "terraform $Command " + ($Arguments -join " ")
    Write-Log "Executing: $fullCommand"
    
    try {
        $process = Start-Process -FilePath "terraform" -ArgumentList ($Command + $Arguments) -WorkingDirectory $TerraformPath -Wait -PassThru -NoNewWindow -RedirectStandardOutput "terraform-output.log" -RedirectStandardError "terraform-error.log"
        
        # Read output files
        if (Test-Path "terraform-output.log") {
            $output = Get-Content "terraform-output.log" -Raw
            if ($output) { Write-Log "Output: $output" }
        }
        
        if (Test-Path "terraform-error.log") {
            $errorOutput = Get-Content "terraform-error.log" -Raw
            if ($errorOutput) { Write-Log "Error Output: $errorOutput" "ERROR" }
        }
        
        if ($process.ExitCode -eq 0) {
            Write-Log "Command completed successfully"
            return $true
        } else {
            Write-Log "Command failed with exit code: $($process.ExitCode)" "ERROR"
            return $false
        }
    } catch {
        Write-Log "Exception running command: $($_.Exception.Message)" "ERROR"
        return $false
    } finally {
        # Cleanup temp files
        @("terraform-output.log", "terraform-error.log") | ForEach-Object {
            if (Test-Path $_) { Remove-Item $_ -Force }
        }
    }
}

# Main execution
try {
    Write-Log "Starting Terraform deployment process..." "INFO"
    Write-Log "Action: $Action, VM Name: $VmName, VM Count: $VmCount"
    
    # Validate prerequisites
    if (!(Test-Prerequisites)) {
        throw "Prerequisites check failed"
    }
    
    # Change to Terraform directory
    Push-Location $TerraformPath
    Write-Log "Working directory: $(Get-Location)"
    
    # Create terraform.tfvars
    $tfvarsPath = New-TerraformVars
    
    # Initialize Terraform
    Write-Log "Initializing Terraform..."
    if (!(Invoke-TerraformCommand "init")) {
        throw "Terraform init failed"
    }
    
    # Execute the requested action
    switch ($Action) {
        "plan" {
            Write-Log "Running Terraform plan..."
            $planArgs = @("-var-file=terraform.tfvars", "-out=tfplan")
            if (!(Invoke-TerraformCommand "plan" $planArgs)) {
                throw "Terraform plan failed"
            }
            Write-Log "Terraform plan completed successfully. Plan saved to tfplan file."
        }
        
        "apply" {
            Write-Log "Running Terraform apply..."
            
            # First run plan to show what will be created
            Write-Log "Generating plan before apply..."
            if (!(Invoke-TerraformCommand "plan" @("-var-file=terraform.tfvars"))) {
                throw "Terraform plan failed"
            }
            
            # Apply changes
            $applyArgs = @("-var-file=terraform.tfvars")
            if ($AutoApprove) {
                $applyArgs += "-auto-approve"
            }
            
            if (!(Invoke-TerraformCommand "apply" $applyArgs 60)) {  # 60 minute timeout for apply
                throw "Terraform apply failed"
            }
            
            Write-Log "Terraform apply completed successfully"
            
            # Get outputs
            Write-Log "Retrieving Terraform outputs..."
            if (Invoke-TerraformCommand "output" @("-json")) {
                Write-Log "Terraform outputs retrieved successfully"
            }
        }
        
        "destroy" {
            Write-Log "Running Terraform destroy..."
            
            if (!$AutoApprove) {
                $confirmation = Read-Host "Are you sure you want to destroy all resources? Type 'yes' to confirm"
                if ($confirmation -ne "yes") {
                    Write-Log "Destroy operation cancelled by user"
                    return
                }
            }
            
            $destroyArgs = @("-var-file=terraform.tfvars")
            if ($AutoApprove) {
                $destroyArgs += "-auto-approve"
            }
            
            if (!(Invoke-TerraformCommand "destroy" $destroyArgs 60)) {  # 60 minute timeout for destroy
                throw "Terraform destroy failed"
            }
            
            Write-Log "Terraform destroy completed successfully"
        }
    }
    
    Write-Log "Terraform deployment process completed successfully" "SUCCESS"
    
    # Return success object
    $result = @{
        Status = "Success"
        Action = $Action
        VmName = $VmName
        VmCount = $VmCount
        ResourceGroup = $ResourceGroupName
        Message = "Terraform $Action completed successfully"
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    
    return $result

} catch {
    Write-Log "Error occurred: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    
    # Return error object
    $result = @{
        Status = "Failed"
        Action = $Action
        VmName = $VmName
        VmCount = $VmCount
        ResourceGroup = $ResourceGroupName
        Message = $_.Exception.Message
        Error = $_.ScriptStackTrace
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    
    return $result
    
} finally {
    # Cleanup
    Pop-Location
    
    if (Test-Path "terraform.tfvars") {
        # Optionally keep the tfvars file for reference
        # Remove-Item "terraform.tfvars" -Force
        Write-Log "terraform.tfvars file preserved for reference"
    }
    
    Write-Log "Script execution completed"
}
