<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 VM Request Form - Hospital IT Department</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-section {
            margin-bottom: 35px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }

        .form-section h3 {
            color: #2E7D32;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .char-counter {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .char-counter.error {
            color: #f44336;
        }

        .char-counter.success {
            color: #4CAF50;
        }

        .preview-section {
            background: #e8f5e8;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
        }

        .preview-section h4 {
            color: #2E7D32;
            margin-bottom: 15px;
        }

        .vm-name-preview {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #1976D2;
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            text-align: center;
        }

        .naming-convention {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
        }

        .naming-convention h4 {
            color: #f57c00;
            margin-bottom: 10px;
        }

        .naming-convention code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #2196F3;
            color: white;
        }

        .btn-success {
            background: #4CAF50;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .validation-message {
            color: #f44336;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }

        .validation-message.show {
            display: block;
        }

        .success-message {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }

        .success-message.show {
            display: block;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
        }

        .info-box h4 {
            color: #1976D2;
            margin-bottom: 10px;
        }

        .info-box ul {
            margin-left: 20px;
            color: #1976D2;
        }

        .info-box li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 VM Request Form</h1>
            <p>Hospital IT Department - Automated VM Deployment</p>
        </div>

        <div class="form-container">
            <div class="info-box">
                <h4>📋 How This Works</h4>
                <ul>
                    <li>Fill out the form with hospital, department, and user information</li>
                    <li>VM name will be automatically generated based on your inputs</li>
                    <li>Submission triggers automated Terraform deployment via DevOps</li>
                    <li>You'll receive notifications about deployment progress</li>
                </ul>
            </div>

            <div class="naming-convention">
                <h4>🏷️ VM Naming Convention</h4>
                <p><strong>Format:</strong> <code>[Hospital Code][Department][User Initials]VM[Type]</code></p>
                <p><strong>Example:</strong> <code>CCH9SUPJDVMCT</code> = Cleveland Clinic Hospital 9 + Supply Department + John Doe + VM + Compute Type</p>
            </div>

            <form id="vmRequestForm">
                <div class="form-section">
                    <h3>🏥 Hospital Information</h3>
                    <div class="form-group">
                        <label for="hospitalCode">Hospital Code * (Exactly 4 characters)</label>
                        <input type="text" id="hospitalCode" name="hospitalCode" required 
                               maxlength="4" placeholder="e.g., CCH9" 
                               oninput="validateHospitalCode(); updateVMPreview()">
                        <div class="char-counter" id="hospitalCounter">0/4 characters</div>
                        <div class="validation-message" id="hospitalValidation">Hospital code must be exactly 4 characters</div>
                    </div>
                </div>

                <div class="form-section">
                    <h3>🏢 Department Information</h3>
                    <div class="form-group">
                        <label for="departmentCode">Department Code * (Exactly 4 characters)</label>
                        <input type="text" id="departmentCode" name="departmentCode" required 
                               maxlength="4" placeholder="e.g., SUPP" 
                               oninput="validateDepartmentCode(); updateVMPreview()">
                        <div class="char-counter" id="departmentCounter">0/4 characters</div>
                        <div class="validation-message" id="departmentValidation">Department code must be exactly 4 characters</div>
                    </div>
                </div>

                <div class="form-section">
                    <h3>👤 User Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required 
                                   placeholder="e.g., John" 
                                   oninput="updateVMPreview()">
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required 
                                   placeholder="e.g., Doe" 
                                   oninput="updateVMPreview()">
                        </div>
                    </div>
                </div>

                <div class="preview-section">
                    <h4>🖥️ Generated VM Name Preview</h4>
                    <div class="vm-name-preview" id="vmNamePreview">
                        Enter information above to see VM name
                    </div>
                </div>

                <div class="form-section">
                    <h3>⚙️ VM Configuration (Optional)</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="vmType">VM Type</label>
                            <select id="vmType" name="vmType" onchange="updateVMPreview()">
                                <option value="CT">Compute (CT)</option>
                                <option value="DB">Database (DB)</option>
                                <option value="WB">Web (WB)</option>
                                <option value="AP">Application (AP)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="vmCount">Number of VMs</label>
                            <input type="number" id="vmCount" name="vmCount" value="1" min="1" max="5">
                        </div>
                    </div>
                </div>

                <div class="button-group">
                    <button type="button" class="btn btn-primary" onclick="validateForm()">🔍 Validate Request</button>
                    <button type="button" class="btn btn-success" id="submitBtn" onclick="submitRequest()" disabled>🚀 Submit VM Request</button>
                </div>
            </form>

            <div id="successMessage" class="success-message">
                <strong>✅ Request Submitted Successfully!</strong><br>
                Your VM deployment request has been submitted and will be processed automatically.
                You'll receive notifications about the deployment progress.
            </div>
        </div>
    </div>

    <script>
        function validateHospitalCode() {
            const input = document.getElementById('hospitalCode');
            const counter = document.getElementById('hospitalCounter');
            const validation = document.getElementById('hospitalValidation');
            const value = input.value.toUpperCase();
            
            input.value = value; // Convert to uppercase
            counter.textContent = `${value.length}/4 characters`;
            
            if (value.length === 4) {
                counter.className = 'char-counter success';
                validation.classList.remove('show');
                return true;
            } else {
                counter.className = 'char-counter error';
                validation.classList.add('show');
                return false;
            }
        }

        function validateDepartmentCode() {
            const input = document.getElementById('departmentCode');
            const counter = document.getElementById('departmentCounter');
            const validation = document.getElementById('departmentValidation');
            const value = input.value.toUpperCase();
            
            input.value = value; // Convert to uppercase
            counter.textContent = `${value.length}/4 characters`;
            
            if (value.length === 4) {
                counter.className = 'char-counter success';
                validation.classList.remove('show');
                return true;
            } else {
                counter.className = 'char-counter error';
                validation.classList.add('show');
                return false;
            }
        }

        function updateVMPreview() {
            const hospitalCode = document.getElementById('hospitalCode').value.toUpperCase();
            const departmentCode = document.getElementById('departmentCode').value.toUpperCase();
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const vmType = document.getElementById('vmType').value;
            
            if (hospitalCode && departmentCode && firstName && lastName) {
                const firstInitial = firstName.charAt(0).toUpperCase();
                const lastInitial = lastName.charAt(0).toUpperCase();
                const vmName = `${hospitalCode}${departmentCode}${firstInitial}${lastInitial}VM${vmType}`;
                document.getElementById('vmNamePreview').textContent = vmName;
            } else {
                document.getElementById('vmNamePreview').textContent = 'Enter information above to see VM name';
            }
        }

        function validateForm() {
            const hospitalValid = validateHospitalCode();
            const departmentValid = validateDepartmentCode();
            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            
            const isValid = hospitalValid && departmentValid && firstName && lastName;
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = !isValid;
            
            if (isValid) {
                alert('✅ Form validation passed! You can now submit the request.');
            } else {
                alert('❌ Please fix the validation errors before submitting.');
            }
        }

        function submitRequest() {
            const formData = new FormData(document.getElementById('vmRequestForm'));
            const hospitalCode = formData.get('hospitalCode');
            const departmentCode = formData.get('departmentCode');
            const firstName = formData.get('firstName');
            const lastName = formData.get('lastName');
            const vmType = formData.get('vmType');
            const vmCount = formData.get('vmCount');
            
            const vmName = `${hospitalCode}${departmentCode}${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}VM${vmType}`;
            
            // This would normally submit to Power Automate
            const requestData = {
                vmName: vmName,
                hospitalCode: hospitalCode,
                departmentCode: departmentCode,
                firstName: firstName,
                lastName: lastName,
                vmType: vmType,
                vmCount: parseInt(vmCount),
                requestedBy: `${firstName} ${lastName}`,
                requestDate: new Date().toISOString(),
                action: 'apply'
            };
            
            console.log('Submitting request:', requestData);
            
            // Show success message
            document.getElementById('successMessage').classList.add('show');
            
            // In real implementation, this would call Power Automate
            // fetch('YOUR_POWER_AUTOMATE_TRIGGER_URL', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(requestData)
            // });
        }

        // Initialize form
        document.addEventListener('DOMContentLoaded', function() {
            updateVMPreview();
        });
    </script>
</body>
</html>
