# Azure DevOps Pipeline for Microsoft Forms VM Deployment Automation
# Triggered by Power Automate Flow

trigger: none # Manual/API trigger only

parameters:
  - name: vmName
    displayName: 'VM Name'
    type: string
    default: ''
  - name: hospitalCode
    displayName: 'Hospital Code'
    type: string
    default: ''
  - name: departmentCode
    displayName: 'Department Code'
    type: string
    default: ''
  - name: firstName
    displayName: 'First Name'
    type: string
    default: ''
  - name: lastName
    displayName: 'Last Name'
    type: string
    default: ''
  - name: deploymentId
    displayName: 'Deployment ID'
    type: string
    default: ''
  - name: requestedBy
    displayName: 'Requested By'
    type: string
    default: 'System Request'

variables:
  - group: terraform-secrets
  - name: vmName
    value: ${{ parameters.vmName }}
  - name: hospitalCode
    value: ${{ parameters.hospitalCode }}
  - name: departmentCode
    value: ${{ parameters.departmentCode }}
  - name: deploymentId
    value: ${{ parameters.deploymentId }}
  - name: requestedBy
    value: ${{ parameters.requestedBy }}

pool:
  vmImage: 'windows-latest'

stages:
  - stage: Validate
    displayName: 'Validate Request'
    jobs:
      - job: ValidateInputs
        displayName: 'Validate Form Inputs'
        steps:
          - task: PowerShell@2
            displayName: 'Validate VM Request Parameters'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "🔍 Validating VM Request Parameters..." -ForegroundColor Cyan
                Write-Host "Deployment ID: $(deploymentId)" -ForegroundColor Yellow
                Write-Host "VM Name: $(vmName)" -ForegroundColor Yellow
                Write-Host "Hospital Code: $(hospitalCode)" -ForegroundColor Yellow
                Write-Host "Department Code: $(departmentCode)" -ForegroundColor Yellow
                Write-Host "Requested By: $(requestedBy)" -ForegroundColor Yellow
                
                # Validate required parameters
                $errors = @()
                
                if ([string]::IsNullOrEmpty("$(vmName)")) {
                    $errors += "VM Name is required"
                }
                
                if ([string]::IsNullOrEmpty("$(hospitalCode)")) {
                    $errors += "Hospital Code is required"
                } elseif ("$(hospitalCode)".Length -gt 4) {
                    $errors += "Hospital Code must be 4 characters or less"
                }
                
                if ([string]::IsNullOrEmpty("$(departmentCode)")) {
                    $errors += "Department Code is required"
                } elseif ("$(departmentCode)".Length -gt 4) {
                    $errors += "Department Code must be 4 characters or less"
                }
                
                if ($errors.Count -gt 0) {
                    Write-Host "❌ Validation Errors:" -ForegroundColor Red
                    foreach ($error in $errors) {
                        Write-Host "  - $error" -ForegroundColor Red
                    }
                    exit 1
                } else {
                    Write-Host "✅ All validations passed!" -ForegroundColor Green
                }

  - stage: Deploy
    displayName: 'Deploy VM'
    dependsOn: Validate
    condition: succeeded()
    jobs:
      - job: TerraformDeploy
        displayName: 'Terraform VM Deployment'
        steps:
          - checkout: self
            displayName: 'Checkout Repository'

          - task: PowerShell@2
            displayName: 'Setup Terraform Variables'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "📝 Creating terraform.tfvars for deployment..." -ForegroundColor Cyan
                
                $tfvarsContent = @"
                vm_name = "$(vmName)"
                vm_count = 1
                vm_resourcegroup = "$(resourceGroup)"
                vm_size = "$(vmSize)"
                vm_sku = "2022-datacenter"
                vm_location = "$(location)"
                vm_admin_username = "$(adminUsername)"
                vm_admin_password = "$(adminPassword)"
                azure_devops_organization = "$(azureDevOpsOrg)"
                azure_devops_teamproject = "$(teamProject)"
                azure_devops_deploymentgroup = "$(deploymentGroup)"
                azure_devops_pat = "$(azureDevOpsPat)"
                azure_devops_agentfolder = "$(agentFolder)"
                
                # Metadata for tracking
                deployment_id = "$(deploymentId)"
                hospital_code = "$(hospitalCode)"
                department_code = "$(departmentCode)"
                requested_by = "$(requestedBy)"
                "@
                
                $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
                Write-Host "✅ terraform.tfvars created successfully" -ForegroundColor Green
                
                # Display content for verification (without sensitive data)
                Write-Host "📋 Terraform Variables (sanitized):" -ForegroundColor Yellow
                Write-Host "VM Name: $(vmName)" -ForegroundColor White
                Write-Host "Resource Group: $(resourceGroup)" -ForegroundColor White
                Write-Host "VM Size: $(vmSize)" -ForegroundColor White
                Write-Host "Location: $(location)" -ForegroundColor White

          - task: TerraformInstaller@0
            displayName: 'Install Terraform'
            inputs:
              terraformVersion: 'latest'

          - task: TerraformTaskV3@3
            displayName: 'Terraform Init'
            inputs:
              provider: 'azurerm'
              command: 'init'
              workingDirectory: '$(System.DefaultWorkingDirectory)/01-Terraform-Core'
              backendServiceArm: '$(azureServiceConnection)'
              backendAzureRmResourceGroupName: '$(terraformStateResourceGroup)'
              backendAzureRmStorageAccountName: '$(terraformStateStorageAccount)'
              backendAzureRmContainerName: 'tfstate'
              backendAzureRmKey: 'terraform.tfstate'

          - task: TerraformTaskV3@3
            displayName: 'Terraform Plan'
            inputs:
              provider: 'azurerm'
              command: 'plan'
              workingDirectory: '$(System.DefaultWorkingDirectory)/01-Terraform-Core'
              environmentServiceNameAzureRM: '$(azureServiceConnection)'
              commandOptions: '-var-file="../terraform.tfvars" -out=tfplan'

          - task: TerraformTaskV3@3
            displayName: 'Terraform Apply'
            inputs:
              provider: 'azurerm'
              command: 'apply'
              workingDirectory: '$(System.DefaultWorkingDirectory)/01-Terraform-Core'
              environmentServiceNameAzureRM: '$(azureServiceConnection)'
              commandOptions: 'tfplan'

          - task: PowerShell@2
            displayName: 'Get Terraform Outputs'
            inputs:
              targetType: 'inline'
              workingDirectory: '$(System.DefaultWorkingDirectory)/01-Terraform-Core'
              script: |
                Write-Host "📊 Getting Terraform outputs..." -ForegroundColor Cyan
                
                try {
                    $outputs = terraform output -json | ConvertFrom-Json
                    Write-Host "✅ Terraform outputs retrieved successfully" -ForegroundColor Green
                    
                    # Set pipeline variables for notification
                    if ($outputs.vm_public_ip) {
                        Write-Host "##vso[task.setvariable variable=vmPublicIP]$($outputs.vm_public_ip.value)"
                    }
                    if ($outputs.vm_private_ip) {
                        Write-Host "##vso[task.setvariable variable=vmPrivateIP]$($outputs.vm_private_ip.value)"
                    }
                } catch {
                    Write-Host "⚠️ Could not retrieve Terraform outputs: $($_.Exception.Message)" -ForegroundColor Yellow
                }

  - stage: Notify
    displayName: 'Send Notifications'
    dependsOn: Deploy
    condition: always()
    jobs:
      - job: SendNotification
        displayName: 'Send Completion Notification'
        steps:
          - task: PowerShell@2
            displayName: 'Send Teams Notification'
            inputs:
              targetType: 'inline'
              script: |
                $deploymentStatus = "$(Agent.JobStatus)"
                $statusEmoji = if ($deploymentStatus -eq "Succeeded") { "✅" } else { "❌" }
                $statusColor = if ($deploymentStatus -eq "Succeeded") { "Good" } else { "Attention" }
                
                Write-Host "$statusEmoji Deployment Status: $deploymentStatus" -ForegroundColor $(if ($deploymentStatus -eq "Succeeded") { "Green" } else { "Red" })
                Write-Host "📧 Sending notification to Teams..." -ForegroundColor Cyan
                
                # Here you would send the actual Teams notification
                # This is a placeholder for the notification logic
                Write-Host "Notification sent for deployment $(deploymentId)" -ForegroundColor Green
