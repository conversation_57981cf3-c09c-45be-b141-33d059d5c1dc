variable "vm_name" {
  description = "Name of the VM to be created"
  type        = string
  default     = "CCH9APPVMCT"
}
variable "vm_count" {
  description = "Count of VMs required"
  default     = "1"
}
variable "vm_resourcegroup" {
  description = "Name of the VM to be created"
  type        = string
  default     = "MAH9-SUP-CCH9-VMC"
}
variable "vm_size" {
  description = "size of the VM to be created"
  type        = string
  default     = "Standard_D4s_v3"
}
variable "vm_offer" {
  description = "Specifies the hardware size of the VM (CPU, RAM, etc.)"
  type        = string
  default     = "WindowsServer"
}
variable "vm_publisher" {
  description = "publisher of the VM to be created"
  type        = string
  default     = "MicrosoftWindowsServer"
}
variable "vm_sku" {
  description = "Refers to the image SKU — the specific version of an OS/image"
  type        = string
  default     = "2022-datacenter-azure-edition"
}
variable "vm_osType" {
  description = "osType of the VM to be created"
  type        = string
  default     = "Windows"
}
variable "vm_location" {
  description = "Location of the VM to be created"
  type        = string
  default     = "Eastus2"
}
variable "vm_admin_username" {
  description = "User Name for the VM to be created"
  type        = string
  default     = "maoperator"
}
variable "vm_admin_password" {
  description = "Password for the VM to be created"
  type        = string
  default     = "M1t1g@t0r"
}
variable "azure_devops_organization" {
  description = "Name or URL of your Azure DevOps organization"
  type        = string
  default     = "https://dev.azure.com/MAHealth"
}
variable "azure_devops_teamproject" {
  description = "Project name for the VM to be created"
  type        = string
  default     = "MAH9-SCP-CCH9GI"
}
variable "azure_devops_deploymentgroup" {
  description = "Deployment group for staging servers"
  type        = string
  default     = "MAH9-SCP-CCH9-DGR-DEV"
}
variable "azure_devops_pat" {
  description = "Personal Access Token (PAT) used to authenticate with Azure DevOps"
  type        = string
  default     = "CVAwdHA384X3n3NRD1r9jspmHnWndGcDSLwZv0yBYXK0jZUbvAgRJQQJ99BEACAAAAA71N4WAAASAZDO1m2O"
}
variable "azure_devops_agentfolder" {
  description = "Folder where the agent created"
  type        = string
  default     = "c:/Agent"
}

