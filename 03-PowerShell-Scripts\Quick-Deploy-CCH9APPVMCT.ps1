# Quick Deployment Script for CCH9APPVMCT
# This script will deploy your VM automatically without any manual intervention

Clear-Host
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🚀 Quick Deploy: CCH9APPVMCT" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Define parameters
$params = @{
    vmName = "CCH9APPVMCT"
    vmCount = 1
    resourceGroup = "MAH9-SUP-CCH9-VMC"
    vmSize = "Standard_D4s_v3"
    vmSku = "2022-datacenter"
    location = "East US 2"
    adminUsername = "maoperator"
    adminPassword = "M1t1g@t0r2025"
    azureDevOpsOrg = "https://dev.azure.com/MAHealth"
    teamProject = "MAH9-SCP-CCH9GI"
    deploymentGroup = "MAH9-SCP-CCH9-DGR-DEV"
    azureDevOpsPat = "4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7"
    agentFolder = "c:/Agent"
}

Write-Host "Creating terraform.tfvars..." -ForegroundColor Cyan

# Create terraform.tfvars
$tfvarsContent = @"
vm_name = "$($params.vmName)"
vm_count = $($params.vmCount)
vm_resourcegroup = "$($params.resourceGroup)"
vm_size = "$($params.vmSize)"
vm_sku = "$($params.vmSku)"
vm_location = "$($params.location)"
vm_admin_username = "$($params.adminUsername)"
vm_admin_password = "$($params.adminPassword)"
azure_devops_organization = "$($params.azureDevOpsOrg)"
azure_devops_teamproject = "$($params.teamProject)"
azure_devops_deploymentgroup = "$($params.deploymentGroup)"
azure_devops_pat = "$($params.azureDevOpsPat)"
azure_devops_agentfolder = "$($params.agentFolder)"
"@

$tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
Write-Host "terraform.tfvars created" -ForegroundColor Green

# Check prerequisites
if (!(Test-Path "main.tf")) {
    Write-Host "main.tf not found in current directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
    Write-Host "Terraform not found in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Prerequisites checked" -ForegroundColor Green
Write-Host ""

try {
    # Initialize Terraform
    Write-Host "Initializing Terraform..." -ForegroundColor Cyan
    terraform init
    if ($LASTEXITCODE -ne 0) { throw "Terraform init failed" }
    Write-Host "Terraform initialized" -ForegroundColor Green

    # Validate
    Write-Host "Validating configuration..." -ForegroundColor Cyan
    terraform validate
    if ($LASTEXITCODE -ne 0) { throw "Terraform validation failed" }
    Write-Host "Configuration validated" -ForegroundColor Green

    # Execute apply
    Write-Host "Running terraform apply..." -ForegroundColor Cyan
    Write-Host "This may take 10-15 minutes..." -ForegroundColor Yellow
    
    terraform apply -var-file="terraform.tfvars" -auto-approve
    if ($LASTEXITCODE -ne 0) { throw "Terraform apply failed" }

    Write-Host ""
    Write-Host "Terraform apply completed successfully!" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "Deployment failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Process completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
