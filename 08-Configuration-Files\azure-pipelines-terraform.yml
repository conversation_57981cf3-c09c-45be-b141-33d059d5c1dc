# Azure DevOps Pipeline for Terraform VM Deployment
# This pipeline allows you to deploy VMs using Terraform with customizable parameters

trigger: none  # Manual trigger only

parameters:
  - name: action
    displayName: 'Terraform Action'
    type: string
    default: 'plan'
    values:
      - plan
      - apply
      - destroy

  - name: vmName
    displayName: 'VM Base Name'
    type: string
    default: 'CCH9APPVMCT'

  - name: vmCount
    displayName: 'Number of VMs'
    type: number
    default: 1
    values:
      - 1
      - 2
      - 3
      - 4
      - 5

  - name: vmSize
    displayName: 'VM Size'
    type: string
    default: 'Standard_D4s_v3'
    values:
      - Standard_B2s
      - Standard_D2s_v3
      - Standard_D4s_v3
      - Standard_D8s_v3
      - Standard_E4s_v3

  - name: vmSku
    displayName: 'Windows Version'
    type: string
    default: '2022-datacenter-azure-edition'
    values:
      - 2019-datacenter
      - 2022-datacenter
      - 2022-datacenter-azure-edition

  - name: location
    displayName: 'Azure Region'
    type: string
    default: 'East US 2'
    values:
      - East US
      - East US 2
      - West US
      - West US 2
      - Central US

  - name: resourceGroup
    displayName: 'Resource Group'
    type: string
    default: 'MAH9-SUP-CCH9-VMC'

  - name: adminUsername
    displayName: 'Admin Username'
    type: string
    default: 'maoperator'

  - name: teamProject
    displayName: 'Azure DevOps Team Project'
    type: string
    default: 'MAH9-SCP-CCH9GI'

  - name: deploymentGroup
    displayName: 'Deployment Group'
    type: string
    default: 'MAH9-SCP-CCH9-DGR-DEV'

  - name: agentFolder
    displayName: 'Agent Installation Folder'
    type: string
    default: 'c:/Agent'

  - name: autoApprove
    displayName: 'Auto Approve (for apply/destroy)'
    type: boolean
    default: false

variables:
  - group: 'terraform-secrets'  # Variable group containing sensitive data
  - name: terraformVersion
    value: '1.5.7'
  - name: workingDirectory
    value: '$(System.DefaultWorkingDirectory)'

pool:
  vmImage: 'windows-latest'

stages:
  - stage: Validate
    displayName: 'Validate Parameters'
    jobs:
      - job: ValidateInputs
        displayName: 'Validate Input Parameters'
        steps:
          - task: PowerShell@2
            displayName: 'Validate Parameters'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "Validating input parameters..."
                
                # Validate VM name
                if ("${{ parameters.vmName }}" -notmatch "^[a-zA-Z0-9-]{1,15}$") {
                    Write-Error "VM name must be 1-15 characters and contain only letters, numbers, and hyphens"
                    exit 1
                }
                
                # Validate VM count
                if (${{ parameters.vmCount }} -lt 1 -or ${{ parameters.vmCount }} -gt 5) {
                    Write-Error "VM count must be between 1 and 5"
                    exit 1
                }
                
                # Validate resource group
                if ("${{ parameters.resourceGroup }}" -eq "") {
                    Write-Error "Resource group cannot be empty"
                    exit 1
                }
                
                Write-Host "✅ All parameters validated successfully"
                Write-Host "Action: ${{ parameters.action }}"
                Write-Host "VM Name: ${{ parameters.vmName }}"
                Write-Host "VM Count: ${{ parameters.vmCount }}"
                Write-Host "VM Size: ${{ parameters.vmSize }}"
                Write-Host "Location: ${{ parameters.location }}"
                Write-Host "Resource Group: ${{ parameters.resourceGroup }}"

  - stage: TerraformDeploy
    displayName: 'Terraform Deployment'
    dependsOn: Validate
    condition: succeeded()
    jobs:
      - job: TerraformJob
        displayName: 'Execute Terraform'
        steps:
          - checkout: self
            displayName: 'Checkout Repository'

          - task: TerraformInstaller@0
            displayName: 'Install Terraform'
            inputs:
              terraformVersion: '$(terraformVersion)'

          - task: AzureCLI@2
            displayName: 'Azure Login'
            inputs:
              azureSubscription: 'Azure-Service-Connection'  # Replace with your service connection name
              scriptType: 'ps'
              scriptLocation: 'inlineScript'
              inlineScript: |
                Write-Host "Logged in to Azure successfully"
                az account show

          - task: PowerShell@2
            displayName: 'Create terraform.tfvars'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "Creating terraform.tfvars file..."
                
                $tfvarsContent = @"
                # Generated terraform.tfvars file
                # Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
                # Pipeline: $(Build.DefinitionName)
                # Build: $(Build.BuildNumber)
                
                vm_name = "${{ parameters.vmName }}"
                vm_count = ${{ parameters.vmCount }}
                vm_resourcegroup = "${{ parameters.resourceGroup }}"
                vm_size = "${{ parameters.vmSize }}"
                vm_sku = "${{ parameters.vmSku }}"
                vm_location = "${{ parameters.location }}"
                vm_admin_username = "${{ parameters.adminUsername }}"
                vm_admin_password = "$(vm-admin-password)"
                azure_devops_organization = "$(azure-devops-organization)"
                azure_devops_teamproject = "${{ parameters.teamProject }}"
                azure_devops_deploymentgroup = "${{ parameters.deploymentGroup }}"
                azure_devops_pat = "$(azure-devops-pat)"
                azure_devops_agentfolder = "${{ parameters.agentFolder }}"
                "@
                
                $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
                Write-Host "✅ terraform.tfvars created successfully"

          - task: PowerShell@2
            displayName: 'Terraform Init'
            inputs:
              targetType: 'inline'
              workingDirectory: '$(workingDirectory)'
              script: |
                Write-Host "Initializing Terraform..."
                terraform init
                if ($LASTEXITCODE -ne 0) {
                    Write-Error "Terraform init failed"
                    exit 1
                }
                Write-Host "✅ Terraform initialized successfully"

          - task: PowerShell@2
            displayName: 'Terraform Validate'
            inputs:
              targetType: 'inline'
              workingDirectory: '$(workingDirectory)'
              script: |
                Write-Host "Validating Terraform configuration..."
                terraform validate
                if ($LASTEXITCODE -ne 0) {
                    Write-Error "Terraform validation failed"
                    exit 1
                }
                Write-Host "✅ Terraform configuration is valid"

          - task: PowerShell@2
            displayName: 'Terraform Plan'
            condition: or(eq('${{ parameters.action }}', 'plan'), eq('${{ parameters.action }}', 'apply'))
            inputs:
              targetType: 'inline'
              workingDirectory: '$(workingDirectory)'
              script: |
                Write-Host "Running Terraform plan..."
                terraform plan -var-file="terraform.tfvars" -out="tfplan"
                if ($LASTEXITCODE -ne 0) {
                    Write-Error "Terraform plan failed"
                    exit 1
                }
                Write-Host "✅ Terraform plan completed successfully"

          - task: PowerShell@2
            displayName: 'Terraform Apply'
            condition: and(eq('${{ parameters.action }}', 'apply'), succeeded())
            inputs:
              targetType: 'inline'
              workingDirectory: '$(workingDirectory)'
              script: |
                Write-Host "Running Terraform apply..."
                
                $applyArgs = @("-var-file=terraform.tfvars")
                if ("${{ parameters.autoApprove }}" -eq "True") {
                    $applyArgs += "-auto-approve"
                    Write-Host "Auto-approve enabled"
                } else {
                    Write-Host "Manual approval required - this will fail in automated pipeline"
                    Write-Host "Set autoApprove parameter to true for automated deployment"
                }
                
                $applyCommand = "terraform apply " + ($applyArgs -join " ")
                Write-Host "Executing: $applyCommand"
                
                Invoke-Expression $applyCommand
                if ($LASTEXITCODE -ne 0) {
                    Write-Error "Terraform apply failed"
                    exit 1
                }
                Write-Host "✅ Terraform apply completed successfully"

          - task: PowerShell@2
            displayName: 'Terraform Destroy'
            condition: and(eq('${{ parameters.action }}', 'destroy'), succeeded())
            inputs:
              targetType: 'inline'
              workingDirectory: '$(workingDirectory)'
              script: |
                Write-Host "Running Terraform destroy..."
                
                $destroyArgs = @("-var-file=terraform.tfvars")
                if ("${{ parameters.autoApprove }}" -eq "True") {
                    $destroyArgs += "-auto-approve"
                    Write-Host "Auto-approve enabled"
                } else {
                    Write-Host "Manual approval required - this will fail in automated pipeline"
                    Write-Host "Set autoApprove parameter to true for automated destruction"
                }
                
                $destroyCommand = "terraform destroy " + ($destroyArgs -join " ")
                Write-Host "Executing: $destroyCommand"
                
                Invoke-Expression $destroyCommand
                if ($LASTEXITCODE -ne 0) {
                    Write-Error "Terraform destroy failed"
                    exit 1
                }
                Write-Host "✅ Terraform destroy completed successfully"

          - task: PowerShell@2
            displayName: 'Get Terraform Outputs'
            condition: and(eq('${{ parameters.action }}', 'apply'), succeeded())
            inputs:
              targetType: 'inline'
              workingDirectory: '$(workingDirectory)'
              script: |
                Write-Host "Retrieving Terraform outputs..."
                terraform output -json > terraform-outputs.json
                if (Test-Path "terraform-outputs.json") {
                    $outputs = Get-Content "terraform-outputs.json" | ConvertFrom-Json
                    Write-Host "Terraform Outputs:"
                    $outputs | ConvertTo-Json -Depth 10 | Write-Host
                } else {
                    Write-Host "No outputs available"
                }

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Terraform Files'
            condition: always()
            inputs:
              pathToPublish: '$(workingDirectory)'
              artifactName: 'terraform-files'
              publishLocation: 'Container'

  - stage: Notification
    displayName: 'Send Notifications'
    dependsOn: TerraformDeploy
    condition: always()
    jobs:
      - job: SendNotification
        displayName: 'Send Teams Notification'
        steps:
          - task: PowerShell@2
            displayName: 'Send Teams Notification'
            inputs:
              targetType: 'inline'
              script: |
                $status = if ("$(Agent.JobStatus)" -eq "Succeeded") { "✅ Success" } else { "❌ Failed" }
                $color = if ("$(Agent.JobStatus)" -eq "Succeeded") { "good" } else { "danger" }
                
                $teamsMessage = @{
                    "@type" = "MessageCard"
                    "@context" = "http://schema.org/extensions"
                    "themeColor" = $color
                    "summary" = "Terraform Deployment $status"
                    "sections" = @(
                        @{
                            "activityTitle" = "🚀 Terraform Deployment $status"
                            "activitySubtitle" = "Pipeline: $(Build.DefinitionName)"
                            "facts" = @(
                                @{ "name" = "Action"; "value" = "${{ parameters.action }}" }
                                @{ "name" = "VM Name"; "value" = "${{ parameters.vmName }}" }
                                @{ "name" = "VM Count"; "value" = "${{ parameters.vmCount }}" }
                                @{ "name" = "Resource Group"; "value" = "${{ parameters.resourceGroup }}" }
                                @{ "name" = "Build Number"; "value" = "$(Build.BuildNumber)" }
                                @{ "name" = "Requested By"; "value" = "$(Build.RequestedFor)" }
                                @{ "name" = "Status"; "value" = "$(Agent.JobStatus)" }
                            )
                        }
                    )
                    "potentialAction" = @(
                        @{
                            "@type" = "OpenUri"
                            "name" = "View Build"
                            "targets" = @(
                                @{ "os" = "default"; "uri" = "$(System.TeamFoundationCollectionUri)$(System.TeamProject)/_build/results?buildId=$(Build.BuildId)" }
                            )
                        }
                    )
                }
                
                $teamsWebhook = "$(teams-webhook-url)"  # Set this in your variable group
                if ($teamsWebhook) {
                    try {
                        Invoke-RestMethod -Uri $teamsWebhook -Method Post -Body ($teamsMessage | ConvertTo-Json -Depth 10) -ContentType "application/json"
                        Write-Host "✅ Teams notification sent successfully"
                    } catch {
                        Write-Warning "Failed to send Teams notification: $($_.Exception.Message)"
                    }
                } else {
                    Write-Host "Teams webhook URL not configured - skipping notification"
                }
