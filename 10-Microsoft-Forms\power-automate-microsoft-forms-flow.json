{"definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"subscriptionId": {"type": "string", "defaultValue": "41ae4571-862f-4682-ac9d-c8e39c9ce301"}, "automationAccountName": {"type": "string", "defaultValue": "terraform-automation"}, "automationResourceGroup": {"type": "string", "defaultValue": "automation-rg"}, "teamsChannelId": {"type": "string", "defaultValue": "your-teams-channel-id"}}, "triggers": {"When_a_new_response_is_submitted": {"type": "ApiConnectionWebhook", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['microsoftforms']['connectionId']"}}, "path": "/formapi/api/forms/@{encodeURIComponent('YOUR_FORM_ID')}/responses", "queries": {"api-version": "1.0"}}}}, "actions": {"Get_response_details": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['microsoftforms']['connectionId']"}}, "method": "get", "path": "/formapi/api/forms/@{encodeURIComponent('YOUR_FORM_ID')}/responses/@{encodeURIComponent(triggerBody()?['resourceData']?['responseId'])}", "queries": {"api-version": "1.0"}}, "runAfter": {}}, "Initialize_Variables": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "deploymentId", "type": "string", "value": "@{concat('DEP-', formatDateTime(utcNow(), 'yyyyMMddHHmmss'), '-', substring(guid(), 0, 8))}"}, {"name": "hospitalCode", "type": "string", "value": "@{toUpper(trim(body('Get_response_details')?['r1c1d1']))}"}, {"name": "departmentCode", "type": "string", "value": "@{toUpper(trim(body('Get_response_details')?['r2c1d1']))}"}, {"name": "firstName", "type": "string", "value": "@{trim(body('Get_response_details')?['r3c1d1'])}"}, {"name": "lastName", "type": "string", "value": "@{trim(body('Get_response_details')?['r3c2d1'])}"}, {"name": "vmType", "type": "string", "value": "@{coalesce(body('Get_response_details')?['r4c1d1'], 'CT')}"}, {"name": "vmCount", "type": "integer", "value": "@{coalesce(int(body('Get_response_details')?['r4c2d1']), 1)}"}]}, "runAfter": {"Get_response_details": ["Succeeded"]}}, "Validate_Input_Data": {"type": "Compose", "inputs": {"hospitalCodeValid": "@{and(not(empty(variables('hospitalCode'))), equals(length(variables('hospitalCode')), 4))}", "departmentCodeValid": "@{and(not(empty(variables('departmentCode'))), equals(length(variables('departmentCode')), 4))}", "userNamesValid": "@{and(not(empty(variables('firstName'))), not(empty(variables('lastName'))))}"}, "runAfter": {"Initialize_Variables": ["Succeeded"]}}, "Check_Validation_Results": {"type": "Condition", "expression": {"and": [{"equals": ["@outputs('Validate_Input_Data')['hospitalCodeValid']", true]}, {"equals": ["@outputs('Validate_Input_Data')['departmentCodeValid']", true]}, {"equals": ["@outputs('Validate_Input_Data')['userNamesValid']", true]}]}, "actions": {"Generate_VM_Name": {"type": "Compose", "inputs": {"vmBaseName": "@{concat(variables('hospitalCode'), variables('departmentCode'), toUpper(substring(variables('firstName'), 0, 1)), toUpper(substring(variables('lastName'), 0, 1)), 'VM', variables('vmType'))}", "vmNames": "@{if(equals(variables('vmCount'), 1), createArray(concat(variables('hospitalCode'), variables('departmentCode'), toUpper(substring(variables('firstName'), 0, 1)), toUpper(substring(variables('lastName'), 0, 1)), 'VM', variables('vmType'))), range(1, variables('vmCount')))}"}}, "Create_Terraform_Variables": {"type": "Compose", "inputs": {"vm_name": "@{outputs('Generate_VM_Name')['vmBaseName']}", "vm_count": "@{variables('vmCount')}", "vm_resourcegroup": "MAH9-SUP-CCH9-VMC", "vm_size": "Standard_D4s_v3", "vm_location": "East US 2", "vm_admin_username": "maoperator", "vm_admin_password": "M1t1g@t0r2025", "azure_devops_organization": "https://dev.azure.com/MAHealth", "azure_devops_teamproject": "MAH9-SCP-CCH9GI", "azure_devops_deploymentgroup": "MAH9-SCP-CCH9-DGR-DEV", "azure_devops_pat": "4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7", "azure_devops_agentfolder": "c:/Agent"}}, "Send_Initial_Notification": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "method": "post", "path": "/flowbot/actions/adaptivecard/recipienttype/channel", "queries": {"recipient": "@parameters('teamsChannelId')"}, "body": {"messageBody": {"type": "AdaptiveCard", "body": [{"type": "TextBlock", "text": "🏥 New VM Request Received", "weight": "Bolder", "size": "Medium", "color": "Accent"}, {"type": "FactSet", "facts": [{"title": "Deployment ID:", "value": "@{variables('deploymentId')}"}, {"title": "Hospital:", "value": "@{variables('hospitalCode')}"}, {"title": "Department:", "value": "@{variables('departmentCode')}"}, {"title": "Requested By:", "value": "@{concat(variables('firstName'), ' ', variables('lastName'))}"}, {"title": "VM Name:", "value": "@{outputs('Generate_VM_Name')['vmBaseName']}"}, {"title": "VM Count:", "value": "@{variables('vmCount')}"}, {"title": "VM Type:", "value": "@{variables('vmType')}"}, {"title": "Status:", "value": "Starting Deployment..."}]}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.2"}}}, "runAfter": {"Create_Terraform_Variables": ["Succeeded"]}}, "Create_PowerShell_Script": {"type": "Compose", "inputs": {"scriptContent": "param(\n    [string]$Action = 'apply',\n    [string]$VmName = '@{outputs('Create_Terraform_Variables')['vm_name']}',\n    [int]$VmCount = @{outputs('Create_Terraform_Variables')['vm_count']},\n    [string]$ResourceGroupName = '@{outputs('Create_Terraform_Variables')['vm_resourcegroup']}',\n    [string]$VmSize = '@{outputs('Create_Terraform_Variables')['vm_size']}',\n    [string]$Location = '@{outputs('Create_Terraform_Variables')['vm_location']}',\n    [string]$AdminUsername = '@{outputs('Create_Terraform_Variables')['vm_admin_username']}',\n    [string]$AdminPassword = '@{outputs('Create_Terraform_Variables')['vm_admin_password']}',\n    [string]$AzureDevOpsOrg = '@{outputs('Create_Terraform_Variables')['azure_devops_organization']}',\n    [string]$TeamProject = '@{outputs('Create_Terraform_Variables')['azure_devops_teamproject']}',\n    [string]$DeploymentGroup = '@{outputs('Create_Terraform_Variables')['azure_devops_deploymentgroup']}',\n    [string]$AzureDevOpsPat = '@{outputs('Create_Terraform_Variables')['azure_devops_pat']}',\n    [string]$AgentFolder = '@{outputs('Create_Terraform_Variables')['azure_devops_agentfolder']}',\n    [string]$DeploymentId = '@{variables('deploymentId')}',\n    [string]$RequestedBy = '@{concat(variables('firstName'), ' ', variables('lastName'))}',\n    [string]$HospitalCode = '@{variables('hospitalCode')}',\n    [string]$DepartmentCode = '@{variables('departmentCode')}'\n)\n\n# Log deployment start\nWrite-Output \"Starting VM deployment for $DeploymentId\"\nWrite-Output \"VM Name: $VmName\"\nWrite-Output \"Requested by: $RequestedBy from $HospitalCode-$DepartmentCode\"\n\n# Import the deployment script\n. .\\Deploy-VM-Terraform.ps1 -Action $Action -VmName $VmName -VmCount $VmCount -ResourceGroupName $ResourceGroupName -VmSize $VmSize -Location $Location -AdminUsername $AdminUsername -AdminPassword $AdminPassword -AzureDevOpsOrg $AzureDevOpsOrg -TeamProject $TeamProject -DeploymentGroup $DeploymentGroup -AzureDevOpsPat $AzureDevOpsPat -AgentFolder $AgentFolder -AutoApprove\n\nWrite-Output \"Deployment completed for $DeploymentId\""}, "runAfter": {"Send_Initial_Notification": ["Succeeded"]}}, "Run_Terraform_Deployment": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureautomation']['connectionId']"}}, "method": "put", "path": "/subscriptions/@{parameters('subscriptionId')}/resourceGroups/@{parameters('automationResourceGroup')}/providers/Microsoft.Automation/automationAccounts/@{parameters('automationAccountName')}/jobs", "queries": {"runbookName": "Deploy-Terraform-VM-Forms", "api-version": "2020-01-13-preview"}, "body": {"properties": {"parameters": {"Action": "apply", "VmName": "@{outputs('Create_Terraform_Variables')['vm_name']}", "VmCount": "@{outputs('Create_Terraform_Variables')['vm_count']}", "ResourceGroupName": "@{outputs('Create_Terraform_Variables')['vm_resourcegroup']}", "VmSize": "@{outputs('Create_Terraform_Variables')['vm_size']}", "Location": "@{outputs('Create_Terraform_Variables')['vm_location']}", "AdminUsername": "@{outputs('Create_Terraform_Variables')['vm_admin_username']}", "AdminPassword": "@{outputs('Create_Terraform_Variables')['vm_admin_password']}", "AzureDevOpsOrg": "@{outputs('Create_Terraform_Variables')['azure_devops_organization']}", "TeamProject": "@{outputs('Create_Terraform_Variables')['azure_devops_teamproject']}", "DeploymentGroup": "@{outputs('Create_Terraform_Variables')['azure_devops_deploymentgroup']}", "AzureDevOpsPat": "@{outputs('Create_Terraform_Variables')['azure_devops_pat']}", "AgentFolder": "@{outputs('Create_Terraform_Variables')['azure_devops_agentfolder']}", "DeploymentId": "@{variables('deploymentId')}", "RequestedBy": "@{concat(variables('firstName'), ' ', variables('lastName'))}", "HospitalCode": "@{variables('hospitalCode')}", "DepartmentCode": "@{variables('departmentCode')}"}}}}, "runAfter": {"Create_PowerShell_Script": ["Succeeded"]}}}, "else": {"actions": {"Send_Validation_Error_Notification": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "method": "post", "path": "/flowbot/actions/adaptivecard/recipienttype/channel", "queries": {"recipient": "@parameters('teamsChannelId')"}, "body": {"messageBody": {"type": "AdaptiveCard", "body": [{"type": "TextBlock", "text": "❌ VM Request Validation Failed", "weight": "Bolder", "size": "Medium", "color": "Attention"}, {"type": "FactSet", "facts": [{"title": "Hospital Code:", "value": "@{concat(variables('hospitalCode'), if(equals(length(variables('hospitalCode')), 4), ' ✅', ' ❌ (Must be 4 characters)'))}"}, {"title": "Department Code:", "value": "@{concat(variables('departmentCode'), if(equals(length(variables('departmentCode')), 4), ' ✅', ' ❌ (Must be 4 characters)'))}"}, {"title": "User Names:", "value": "@{concat(variables('firstName'), ' ', variables('lastName'), if(and(not(empty(variables('firstName'))), not(empty(variables('lastName')))), ' ✅', ' ❌ (Both names required)'))}"}]}, {"type": "TextBlock", "text": "Please correct the form data and resubmit.", "wrap": true}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.2"}}}}}}, "runAfter": {"Validate_Input_Data": ["Succeeded"]}}}, "outputs": {}}, "parameters": {"$connections": {"value": {"microsoftforms": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/microsoftforms", "connectionName": "microsoftforms", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/microsoftforms"}, "teams": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/teams", "connectionName": "teams", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/teams"}, "azureautomation": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/azureautomation", "connectionName": "azureautomation", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/azureautomation"}}}}}