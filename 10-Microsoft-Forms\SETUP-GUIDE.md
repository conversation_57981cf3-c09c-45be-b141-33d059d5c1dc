# Microsoft Forms VM Deployment - Setup Guide

## 🚀 Quick Start Guide

This guide will help you set up the complete Microsoft Forms to VM deployment automation in your environment.

## ✅ Prerequisites Checklist

Before starting, ensure you have:

- [ ] **Azure Subscription** with Contributor access
- [ ] **Azure DevOps Organization** with Project Admin rights
- [ ] **Microsoft 365** with Forms and Power Automate licenses
- [ ] **Microsoft Teams** for notifications
- [ ] **Terraform** knowledge and existing Terraform setup
- [ ] **PowerShell 5.1+** on deployment machine

## 📋 Step-by-Step Setup

### Step 1: Prepare Your Environment

1. **Clone/Download Files**
   ```powershell
   # Navigate to your Terraform directory
   cd "D:\Maspects\MAHealth\Terraform\Scope_Demo-Environment"
   
   # Create the Microsoft Forms directory
   mkdir "10-Microsoft-Forms"
   
   # Copy all files from this solution to the directory
   ```

2. **Run Initial Tests**
   ```powershell
   cd "10-Microsoft-Forms"
   .\test-vm-deployment.ps1 -RunValidationTests
   ```

### Step 2: Configure Microsoft Forms

1. **Create New Microsoft Form**
   - Go to [Microsoft Forms](https://forms.microsoft.com)
   - Click "New Form"
   - Title: "VM Request Form - Hospital IT"

2. **Add Questions** (in this exact order):
   
   **Question 1: Hospital Code**
   - Type: Text
   - Required: Yes
   - Description: "Enter your 4-character hospital code (e.g., CCH9)"
   
   **Question 2: Department Code**
   - Type: Text  
   - Required: Yes
   - Description: "Enter your 4-character department code (e.g., SUPP)"
   
   **Question 3: First Name**
   - Type: Text
   - Required: Yes
   
   **Question 4: Last Name**
   - Type: Text
   - Required: Yes
   
   **Question 5: VM Type**
   - Type: Choice (single select)
   - Options: CT, DB, WB, AP, DC, FS, PS, TS
   - Default: CT
   
   **Question 6: Number of VMs**
   - Type: Text (Number)
   - Default: 1
   - Description: "Enter number between 1-5"

3. **Get Form ID**
   - Share the form and copy the URL
   - Extract the Form ID from the URL (the long string after `/forms/`)
   - Save this ID for Power Automate configuration

### Step 3: Set Up Azure DevOps

1. **Create Variable Group**
   ```yaml
   # In Azure DevOps, go to Pipelines > Library
   # Create new Variable Group: "terraform-secrets"
   # Add these variables:
   
   azureServiceConnection: "your-azure-service-connection"
   resourceGroup: "MAH9-SUP-CCH9-VMC"
   vmSize: "Standard_D4s_v3"
   location: "East US 2"
   adminUsername: "maoperator"
   adminPassword: "your-secure-password" # Mark as secret
   azureDevOpsOrg: "https://dev.azure.com/MAHealth"
   teamProject: "MAH9-SCP-CCH9GI"
   deploymentGroup: "MAH9-SCP-CCH9-DGR-DEV"
   azureDevOpsPat: "your-pat-token" # Mark as secret
   agentFolder: "c:/Agent"
   terraformStateResourceGroup: "terraform-state-rg"
   terraformStateStorageAccount: "terraformstatestorage"
   ```

2. **Create Pipeline**
   - Go to Pipelines > New Pipeline
   - Choose "Azure Repos Git" or your source
   - Select "Existing Azure Pipelines YAML file"
   - Choose `/10-Microsoft-Forms/azure-pipelines-forms.yml`
   - Save (don't run yet)

3. **Configure Service Connections**
   - Go to Project Settings > Service Connections
   - Create "Azure Resource Manager" connection
   - Use Service Principal authentication
   - Name it to match your variable group

### Step 4: Configure Power Automate

1. **Import Flow**
   - Go to [Power Automate](https://flow.microsoft.com)
   - Click "Import" > "Import Package (Legacy)"
   - Upload `power-automate-microsoft-forms-flow.json`

2. **Update Connections**
   - Microsoft Forms: Create new connection
   - Microsoft Teams: Create new connection  
   - Azure Automation: Create new connection (if using)

3. **Configure Flow Parameters**
   ```json
   {
     "subscriptionId": "your-azure-subscription-id",
     "automationAccountName": "terraform-automation",
     "automationResourceGroup": "automation-rg",
     "teamsChannelId": "your-teams-channel-id"
   }
   ```

4. **Update Form ID**
   - In the trigger "When a new response is submitted"
   - Replace `YOUR_FORM_ID` with your actual Form ID
   - In "Get response details" action, update Form ID there too

5. **Configure Teams Channel**
   - Get your Teams Channel ID:
     - In Teams, click "..." on channel > "Get link to channel"
     - Extract the channel ID from the URL
   - Update the `teamsChannelId` parameter

### Step 5: Update Terraform Configuration

1. **Backup Current Configuration**
   ```powershell
   cp "01-Terraform-Core\variable.tf" "01-Terraform-Core\variable.tf.backup"
   cp "01-Terraform-Core\main.tf" "01-Terraform-Core\main.tf.backup"
   ```

2. **Integrate New Variables**
   ```powershell
   # Copy the new variables file
   cp "10-Microsoft-Forms\terraform-variables-forms.tf" "01-Terraform-Core\"
   
   # Update main.tf to include new variables (manual step)
   # Add this line to main.tf:
   # source = "./terraform-variables-forms.tf"
   ```

3. **Test Terraform Configuration**
   ```powershell
   cd "01-Terraform-Core"
   terraform init
   terraform validate
   ```

### Step 6: Test the Complete Workflow

1. **Test Form Validation**
   ```powershell
   cd "10-Microsoft-Forms"
   # Open vm-request-form.html in browser
   # Test various input combinations
   ```

2. **Test PowerShell Script**
   ```powershell
   .\Deploy-VM-Microsoft-Forms.ps1 -HospitalCode "TEST" -DepartmentCode "DEPT" -FirstName "Test" -LastName "User" -Action "plan" -AzureDevOpsPat "your-pat"
   ```

3. **Test End-to-End**
   - Submit a test form
   - Monitor Power Automate flow execution
   - Check Azure DevOps pipeline
   - Verify Teams notifications

### Step 7: Production Deployment

1. **Update Production Settings**
   ```powershell
   # Update variable group with production values
   # Update Form with production branding
   # Configure production Teams channel
   ```

2. **Enable Flow**
   - In Power Automate, turn on the flow
   - Test with a real form submission

3. **Monitor and Validate**
   - Submit test requests
   - Monitor deployment success
   - Verify VM creation and configuration

## 🔧 Configuration Customization

### Modify VM Naming Convention

To change the naming pattern, update these files:
- `vm-name-generator.js` - Line 67
- `Deploy-VM-Microsoft-Forms.ps1` - Line 89
- `power-automate-microsoft-forms-flow.json` - Line 67

### Add New VM Types

1. Update validation in `validation-and-error-handling.js`:
   ```javascript
   validValues: ['CT', 'DB', 'WB', 'AP', 'DC', 'FS', 'PS', 'TS', 'NEW_TYPE']
   ```

2. Update Terraform variables in `terraform-variables-forms.tf`:
   ```hcl
   validation {
     condition = contains(["CT", "DB", "WB", "AP", "DC", "FS", "PS", "TS", "NEW_TYPE"], var.vm_type_code)
   }
   ```

3. Update Microsoft Form choices

### Modify Resource Defaults

Update the variable group in Azure DevOps:
- `vmSize`: Change default VM size
- `location`: Change default Azure region
- `resourceGroup`: Change target resource group

## 🚨 Troubleshooting

### Common Issues

1. **Form ID Not Found**
   - Verify Form ID is correct in Power Automate
   - Check Form sharing permissions
   - Ensure Form is published

2. **Pipeline Fails**
   - Check service connection permissions
   - Verify variable group values
   - Review Terraform state backend

3. **VM Name Too Long**
   - Azure VM names limited to 15 characters
   - Consider shorter hospital/department codes
   - Modify naming convention if needed

4. **Power Automate Timeout**
   - Increase timeout in flow configuration
   - Check Azure DevOps pipeline duration
   - Consider async processing

### Debug Commands

```powershell
# Test form validation
.\test-vm-deployment.ps1 -RunValidationTests -Verbose

# Test integration
.\test-vm-deployment.ps1 -RunIntegrationTests

# Test PowerShell script syntax
.\test-vm-deployment.ps1 -RunAllTests

# Manual deployment test
.\Deploy-VM-Microsoft-Forms.ps1 -HospitalCode "TEST" -DepartmentCode "DEPT" -FirstName "Debug" -LastName "Test" -Action "plan" -AzureDevOpsPat "your-pat"
```

## 📞 Support

### Getting Help

1. **Form Issues**: Check Microsoft Forms documentation
2. **Power Automate Issues**: Review flow execution history
3. **Pipeline Issues**: Check Azure DevOps logs
4. **Terraform Issues**: Review Terraform state and logs

### Escalation Path

1. **Level 1**: Self-service using troubleshooting guide
2. **Level 2**: IT Support for configuration issues
3. **Level 3**: DevOps team for infrastructure issues

## 🎯 Success Criteria

Your setup is successful when:

- [ ] Form validates input correctly
- [ ] VM names generate as expected
- [ ] Power Automate flow executes without errors
- [ ] Azure DevOps pipeline completes successfully
- [ ] VMs are created with correct configuration
- [ ] Teams notifications are received
- [ ] All tests pass

## 📈 Next Steps

After successful setup:

1. **Train Users**: Provide form access to hospital staff
2. **Monitor Usage**: Track form submissions and deployment success
3. **Optimize**: Adjust VM sizes and configurations based on usage
4. **Scale**: Add more hospital codes and departments as needed
5. **Enhance**: Consider adding approval workflows for production

---

**🎉 Congratulations!** You now have a fully automated VM deployment system that transforms form submissions into infrastructure deployments with zero manual intervention.
