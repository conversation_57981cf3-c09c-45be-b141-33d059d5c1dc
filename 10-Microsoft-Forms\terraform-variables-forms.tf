# Terraform Variables for Microsoft Forms Integration
# This file extends the base Terraform configuration to support form-driven deployments

# Core VM Configuration
variable "vm_name" {
  description = "Generated VM name from Microsoft Form (format: [HospitalCode][DeptCode][UserInitials]VM[Type])"
  type        = string
  validation {
    condition     = can(regex("^[A-Z0-9]{4}[A-Z]{4}[A-Z]{2}VM[A-Z]{2}$", var.vm_name))
    error_message = "VM name must follow format: [4-char hospital code][4-char dept code][2-char user initials]VM[2-char type]."
  }
}

variable "vm_count" {
  description = "Number of VMs to create"
  type        = number
  default     = 1
  validation {
    condition     = var.vm_count >= 1 && var.vm_count <= 5
    error_message = "VM count must be between 1 and 5."
  }
}

# Form-specific metadata variables
variable "hospital_code" {
  description = "4-character hospital code from form"
  type        = string
  validation {
    condition     = can(regex("^[A-Z0-9]{4}$", var.hospital_code))
    error_message = "Hospital code must be exactly 4 alphanumeric characters."
  }
}

variable "department_code" {
  description = "4-character department code from form"
  type        = string
  validation {
    condition     = can(regex("^[A-Z]{4}$", var.department_code))
    error_message = "Department code must be exactly 4 letters."
  }
}

variable "user_first_name" {
  description = "First name of the user requesting the VM"
  type        = string
  validation {
    condition     = length(var.user_first_name) >= 2
    error_message = "First name must be at least 2 characters."
  }
}

variable "user_last_name" {
  description = "Last name of the user requesting the VM"
  type        = string
  validation {
    condition     = length(var.user_last_name) >= 2
    error_message = "Last name must be at least 2 characters."
  }
}

variable "vm_type_code" {
  description = "2-character VM type code (CT=Compute, DB=Database, WB=Web, AP=Application)"
  type        = string
  default     = "CT"
  validation {
    condition     = contains(["CT", "DB", "WB", "AP", "DC", "FS", "PS", "TS"], var.vm_type_code)
    error_message = "VM type must be one of: CT, DB, WB, AP, DC, FS, PS, TS."
  }
}

# Deployment tracking variables
variable "deployment_id" {
  description = "Unique deployment ID generated by Power Automate"
  type        = string
  default     = ""
}

variable "requested_by" {
  description = "Full name of the person requesting the deployment"
  type        = string
  default     = ""
}

variable "request_date" {
  description = "Date and time when the request was submitted"
  type        = string
  default     = ""
}

# Azure Infrastructure Configuration
variable "vm_resourcegroup" {
  description = "Azure resource group for the VM"
  type        = string
  default     = "MAH9-SUP-CCH9-VMC"
}

variable "vm_size" {
  description = "Azure VM size/SKU"
  type        = string
  default     = "Standard_D4s_v3"
  validation {
    condition = contains([
      "Standard_D2s_v3", "Standard_D4s_v3", "Standard_D8s_v3", "Standard_D16s_v3",
      "Standard_B2s", "Standard_B4ms", "Standard_B8ms"
    ], var.vm_size)
    error_message = "VM size must be a valid Azure VM SKU."
  }
}

variable "vm_location" {
  description = "Azure region for VM deployment"
  type        = string
  default     = "East US 2"
}

variable "vm_offer" {
  description = "VM image offer"
  type        = string
  default     = "WindowsServer"
}

variable "vm_publisher" {
  description = "VM image publisher"
  type        = string
  default     = "MicrosoftWindowsServer"
}

variable "vm_sku" {
  description = "VM image SKU"
  type        = string
  default     = "2022-datacenter-azure-edition"
}

variable "vm_admin_username" {
  description = "VM administrator username"
  type        = string
  default     = "maoperator"
}

variable "vm_admin_password" {
  description = "VM administrator password"
  type        = string
  sensitive   = true
  default     = "M1t1g@t0r2025"
}

# Azure DevOps Configuration
variable "azure_devops_organization" {
  description = "Azure DevOps organization URL"
  type        = string
  default     = "https://dev.azure.com/MAHealth"
}

variable "azure_devops_teamproject" {
  description = "Azure DevOps team project name"
  type        = string
  default     = "MAH9-SCP-CCH9GI"
}

variable "azure_devops_deploymentgroup" {
  description = "Azure DevOps deployment group name"
  type        = string
  default     = "MAH9-SCP-CCH9-DGR-DEV"
}

variable "azure_devops_pat" {
  description = "Azure DevOps Personal Access Token"
  type        = string
  sensitive   = true
}

variable "azure_devops_agentfolder" {
  description = "Azure DevOps agent installation folder"
  type        = string
  default     = "c:/Agent"
}

# Local values for computed names and tags
locals {
  # Generate user initials
  user_initials = "${upper(substr(var.user_first_name, 0, 1))}${upper(substr(var.user_last_name, 0, 1))}"
  
  # Validate generated VM name matches the provided one
  expected_vm_name = "${var.hospital_code}${var.department_code}${local.user_initials}VM${var.vm_type_code}"
  
  # Common tags for all resources
  common_tags = {
    Environment     = "Production"
    Project         = "Hospital-VM-Automation"
    HospitalCode    = var.hospital_code
    DepartmentCode  = var.department_code
    RequestedBy     = var.requested_by
    DeploymentId    = var.deployment_id
    RequestDate     = var.request_date
    VMType          = var.vm_type_code
    ManagedBy       = "Terraform"
    CreatedBy       = "Microsoft-Forms-Automation"
  }
  
  # VM type descriptions for documentation
  vm_type_descriptions = {
    "CT" = "Compute Server"
    "DB" = "Database Server"
    "WB" = "Web Server"
    "AP" = "Application Server"
    "DC" = "Domain Controller"
    "FS" = "File Server"
    "PS" = "Print Server"
    "TS" = "Terminal Server"
  }
}

# Validation check
resource "null_resource" "validate_vm_name" {
  count = var.vm_name != local.expected_vm_name ? 1 : 0
  
  provisioner "local-exec" {
    command = "echo 'ERROR: VM name mismatch. Expected: ${local.expected_vm_name}, Got: ${var.vm_name}' && exit 1"
  }
}

# Output values for verification and logging
output "deployment_summary" {
  description = "Summary of the deployment configuration"
  value = {
    deployment_id    = var.deployment_id
    vm_name         = var.vm_name
    vm_count        = var.vm_count
    hospital_code   = var.hospital_code
    department_code = var.department_code
    user_name       = "${var.user_first_name} ${var.user_last_name}"
    user_initials   = local.user_initials
    vm_type         = "${var.vm_type_code} (${lookup(local.vm_type_descriptions, var.vm_type_code, "Unknown")})"
    requested_by    = var.requested_by
    request_date    = var.request_date
    resource_group  = var.vm_resourcegroup
    vm_size         = var.vm_size
    location        = var.vm_location
  }
}

output "generated_vm_names" {
  description = "List of VM names that will be created"
  value = var.vm_count == 1 ? [var.vm_name] : [
    for i in range(1, var.vm_count + 1) : "${var.vm_name}${format("%02d", i)}"
  ]
}

output "resource_tags" {
  description = "Tags that will be applied to all resources"
  value = local.common_tags
}
