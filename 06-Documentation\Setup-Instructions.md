# VM Deployment Automation Setup Guide

This guide provides comprehensive instructions for setting up automated VM deployment using Terraform with multiple deployment methods.

## 📋 Overview

You now have four different ways to deploy VMs using Terraform:

1. **HTML Form** - Web-based form for easy parameter input
2. **PowerShell Script** - Direct command-line automation
3. **Power Automate Flow** - Microsoft Power Platform integration
4. **Azure DevOps Pipeline** - CI/CD pipeline with parameters

## 🚀 Quick Start

### Method 1: HTML Form (Easiest)

1. Open `vm-deployment-form.html` in your web browser
2. Fill in the VM parameters (VM name, count, size, etc.)
3. Click "Generate terraform.tfvars" to create the configuration file
4. Use "Plan Deployment" or "Deploy Infrastructure" buttons

### Method 2: PowerShell Script (Direct)

```powershell
# Example usage
.\Deploy-VM-Terraform.ps1 -Action "plan" -VmName "MyVM" -VmCount 2 -ResourceGroupName "my-rg" -AdminUsername "admin" -AdminPassword "SecurePass123!" -AzureDevOpsOrg "https://dev.azure.com/myorg" -TeamProject "myproject" -DeploymentGroup "mygroup" -AzureDevOpsPat "your-pat-token"
```

### Method 3: Power Automate (Workflow)

1. Import `power-automate-form-integration.json` into Power Automate
2. Configure connections (Teams, Azure Automation)
3. Set up Azure Automation Account with the PowerShell script
4. Trigger via HTTP request or integrate with the HTML form

### Method 4: Azure DevOps Pipeline (CI/CD)

1. Add `azure-pipelines-terraform.yml` to your Azure DevOps repository
2. Create a new pipeline using this YAML file
3. Set up variable groups and service connections
4. Run pipeline with parameters

## 🔧 Prerequisites

### General Requirements

- **Azure Subscription** with appropriate permissions
- **Terraform** installed (version 1.5.7 or later)
- **Azure CLI** installed and authenticated (`az login`)
- **PowerShell** 5.1 or PowerShell Core 7+

### For Power Automate

- **Power Automate Premium** license
- **Azure Automation Account** 
- **Teams** workspace (for notifications)

### For Azure DevOps

- **Azure DevOps** organization and project
- **Service Principal** with Contributor access to Azure subscription
- **Variable Groups** configured with secrets

## 📁 File Structure

```
├── vm-deployment-form.html              # Web form for parameter input
├── Deploy-VM-Terraform.ps1              # PowerShell automation script
├── power-automate-form-integration.json # Power Automate flow definition
├── azure-pipelines-terraform.yml        # Azure DevOps pipeline
├── main.tf                             # Terraform main configuration
├── variable.tf                         # Terraform variables
└── Setup-Instructions.md               # This file
```

## ⚙️ Configuration

### 1. HTML Form Configuration

The form is ready to use out of the box. To integrate with Power Automate:

1. Deploy the Power Automate flow
2. Get the HTTP trigger URL
3. Update the `powerAutomateUrl` variable in the HTML form
4. Uncomment the fetch code in the `callPowerAutomate` function

### 2. PowerShell Script Configuration

The script accepts all parameters via command line. Key parameters:

- `Action`: plan, apply, or destroy
- `VmName`: Base name for VMs
- `VmCount`: Number of VMs to create
- `ResourceGroupName`: Azure resource group
- `AdminUsername/AdminPassword`: VM credentials
- `AzureDevOps*`: Azure DevOps configuration

### 3. Power Automate Configuration

1. **Import the Flow**:
   - Go to Power Automate portal
   - Import the JSON file
   - Configure connections

2. **Set up Azure Automation**:
   ```powershell
   # Create Automation Account
   New-AzAutomationAccount -ResourceGroupName "automation-rg" -Name "terraform-automation" -Location "East US 2"
   
   # Import PowerShell script as runbook
   Import-AzAutomationRunbook -AutomationAccountName "terraform-automation" -ResourceGroupName "automation-rg" -Name "Deploy-Terraform-VM" -Type PowerShell -Path ".\Deploy-VM-Terraform.ps1"
   ```

3. **Configure Teams Notifications**:
   - Create Teams webhook
   - Update the channel ID in the flow

### 4. Azure DevOps Configuration

1. **Create Variable Group** (`terraform-secrets`):
   ```
   vm-admin-password: [VM admin password]
   azure-devops-organization: https://dev.azure.com/yourorg
   azure-devops-pat: [Your PAT token]
   teams-webhook-url: [Teams webhook URL]
   ```

2. **Create Service Connection**:
   - Go to Project Settings > Service connections
   - Create new Azure Resource Manager connection
   - Name it "Azure-Service-Connection"

3. **Set up Pipeline**:
   - Create new pipeline
   - Use existing Azure Pipelines YAML file
   - Select `azure-pipelines-terraform.yml`

## 🔐 Security Considerations

### Secrets Management

- **Never commit secrets** to version control
- Use **Azure Key Vault** for production environments
- **Rotate PAT tokens** regularly
- Use **Managed Identities** where possible

### Access Control

- **Limit Terraform permissions** to specific resource groups
- **Use separate service principals** for different environments
- **Enable audit logging** for all deployments
- **Implement approval workflows** for production deployments

## 🧪 Testing

### Test the PowerShell Script

```powershell
# Test with plan action first
.\Deploy-VM-Terraform.ps1 -Action "plan" -VmName "TestVM" -VmCount 1 -ResourceGroupName "test-rg" -AdminUsername "testadmin" -AdminPassword "TestPass123!" -AzureDevOpsOrg "https://dev.azure.com/test" -TeamProject "test" -DeploymentGroup "test" -AzureDevOpsPat "test-token"
```

### Test the HTML Form

1. Open the form in a browser
2. Fill in test values
3. Click "Generate terraform.tfvars"
4. Verify the generated file is correct

### Test Power Automate Flow

1. Use the HTTP trigger URL with a test payload
2. Monitor the flow execution
3. Check Teams notifications
4. Verify Azure Automation job completion

## 🐛 Troubleshooting

### Common Issues

1. **Terraform Init Fails**:
   - Check Azure CLI authentication
   - Verify subscription permissions
   - Ensure Terraform is in PATH

2. **VM Extension Fails**:
   - Check Chocolatey installation timing
   - Verify Azure DevOps PAT permissions
   - Review VM extension logs in Azure portal

3. **Power Automate Timeout**:
   - Increase timeout limits
   - Check Azure Automation account status
   - Verify runbook permissions

4. **Pipeline Fails**:
   - Check service connection configuration
   - Verify variable group values
   - Review pipeline logs

### Debugging Tips

- **Enable verbose logging** in PowerShell scripts
- **Use terraform plan** before apply
- **Check Azure Activity Log** for resource creation issues
- **Monitor Azure Automation job output**

## 📞 Support

For issues or questions:

1. Check the troubleshooting section above
2. Review Azure portal logs
3. Check Terraform state files
4. Verify all prerequisites are met

## 🔄 Updates and Maintenance

### Regular Tasks

- **Update Terraform version** in scripts and pipelines
- **Rotate secrets** (PAT tokens, passwords)
- **Review and update** VM sizes and SKUs
- **Test deployment process** regularly

### Version Control

- **Tag releases** for stable versions
- **Use branches** for testing changes
- **Document changes** in commit messages
- **Backup Terraform state** files

---

## 📝 Example Usage Scenarios

### Scenario 1: Development Environment

```powershell
.\Deploy-VM-Terraform.ps1 -Action "apply" -VmName "DevVM" -VmCount 1 -ResourceGroupName "dev-rg" -VmSize "Standard_B2s" -AutoApprove
```

### Scenario 2: Testing Environment

Use the HTML form with:
- VM Name: TestVM
- VM Count: 2
- VM Size: Standard_D2s_v3
- Auto-approve: No (for manual verification)

### Scenario 3: Production Deployment

Use Azure DevOps pipeline with:
- Manual approval gates
- Separate variable groups for prod
- Notification to multiple Teams channels
- Backup and rollback procedures

This setup provides a comprehensive, flexible solution for automated VM deployment with multiple interfaces and robust error handling.
