# Deploy-VM-Microsoft-Forms.ps1
# PowerShell script for deploying VMs based on Microsoft Forms submissions
# Integrates with Power Automate and Azure DevOps for fully automated deployment

param(
    [Parameter(Mandatory=$true)]
    [string]$HospitalCode,
    
    [Parameter(Mandatory=$true)]
    [string]$DepartmentCode,
    
    [Parameter(Mandatory=$true)]
    [string]$FirstName,
    
    [Parameter(Mandatory=$true)]
    [string]$LastName,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("CT", "DB", "WB", "AP", "DC", "FS", "PS", "TS")]
    [string]$VMType = "CT",
    
    [Parameter(Mandatory=$false)]
    [ValidateRange(1, 5)]
    [int]$VMCount = 1,
    
    [Parameter(Mandatory=$false)]
    [string]$DeploymentId = "",
    
    [Parameter(Mandatory=$false)]
    [string]$ResourceGroup = "MAH9-SUP-CCH9-VMC",
    
    [Parameter(Mandatory=$false)]
    [string]$VMSize = "Standard_D4s_v3",
    
    [Parameter(Mandatory=$false)]
    [string]$Location = "East US 2",
    
    [Parameter(Mandatory=$false)]
    [string]$AdminUsername = "maoperator",
    
    [Parameter(Mandatory=$false)]
    [string]$AdminPassword = "M1t1g@t0r2025",
    
    [Parameter(Mandatory=$false)]
    [string]$AzureDevOpsOrg = "https://dev.azure.com/MAHealth",
    
    [Parameter(Mandatory=$false)]
    [string]$TeamProject = "MAH9-SCP-CCH9GI",
    
    [Parameter(Mandatory=$false)]
    [string]$DeploymentGroup = "MAH9-SCP-CCH9-DGR-DEV",
    
    [Parameter(Mandatory=$true)]
    [string]$AzureDevOpsPat,
    
    [Parameter(Mandatory=$false)]
    [string]$AgentFolder = "c:/Agent",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("plan", "apply", "destroy")]
    [string]$Action = "apply",
    
    [Parameter(Mandatory=$false)]
    [switch]$AutoApprove
)

# Function to write timestamped log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Output "[$timestamp] [$Level] $Message"
}

# Function to validate input parameters
function Test-InputValidation {
    param(
        [string]$Hospital,
        [string]$Department,
        [string]$First,
        [string]$Last
    )
    
    $errors = @()
    
    if ($Hospital.Length -ne 4) {
        $errors += "Hospital code must be exactly 4 characters"
    }
    
    if ($Department.Length -ne 4) {
        $errors += "Department code must be exactly 4 characters"
    }
    
    if ($First.Length -lt 2) {
        $errors += "First name must be at least 2 characters"
    }
    
    if ($Last.Length -lt 2) {
        $errors += "Last name must be at least 2 characters"
    }
    
    if ($Hospital -notmatch '^[A-Z0-9]+$') {
        $errors += "Hospital code must contain only letters and numbers"
    }
    
    if ($Department -notmatch '^[A-Z]+$') {
        $errors += "Department code must contain only letters"
    }
    
    return $errors
}

# Function to generate VM name
function New-VMName {
    param(
        [string]$Hospital,
        [string]$Department,
        [string]$First,
        [string]$Last,
        [string]$Type
    )
    
    $firstInitial = $First.Substring(0, 1).ToUpper()
    $lastInitial = $Last.Substring(0, 1).ToUpper()
    
    return "$Hospital$Department$firstInitial$lastInitial" + "VM$Type"
}

# Function to create Terraform variables file
function New-TerraformVarsFile {
    param(
        [string]$VMName,
        [hashtable]$Parameters
    )
    
    $varsContent = @"
# Terraform variables generated from Microsoft Forms submission
# Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
# Deployment ID: $($Parameters.DeploymentId)

# Core VM Configuration
vm_name = "$VMName"
vm_count = $($Parameters.VMCount)
vm_resourcegroup = "$($Parameters.ResourceGroup)"
vm_size = "$($Parameters.VMSize)"
vm_location = "$($Parameters.Location)"

# Form-specific metadata
hospital_code = "$($Parameters.HospitalCode)"
department_code = "$($Parameters.DepartmentCode)"
user_first_name = "$($Parameters.FirstName)"
user_last_name = "$($Parameters.LastName)"
vm_type_code = "$($Parameters.VMType)"

# Deployment tracking
deployment_id = "$($Parameters.DeploymentId)"
requested_by = "$($Parameters.FirstName) $($Parameters.LastName)"
request_date = "$(Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")"

# VM Configuration
vm_offer = "WindowsServer"
vm_publisher = "MicrosoftWindowsServer"
vm_sku = "2022-datacenter-azure-edition"
vm_admin_username = "$($Parameters.AdminUsername)"
vm_admin_password = "$($Parameters.AdminPassword)"

# Azure DevOps Configuration
azure_devops_organization = "$($Parameters.AzureDevOpsOrg)"
azure_devops_teamproject = "$($Parameters.TeamProject)"
azure_devops_deploymentgroup = "$($Parameters.DeploymentGroup)"
azure_devops_pat = "$($Parameters.AzureDevOpsPat)"
azure_devops_agentfolder = "$($Parameters.AgentFolder)"
"@

    $varsFile = "terraform-forms-$($Parameters.DeploymentId).tfvars"
    $varsContent | Out-File -FilePath $varsFile -Encoding UTF8
    
    Write-Log "Created Terraform variables file: $varsFile"
    return $varsFile
}

# Main execution starts here
try {
    Write-Log "Starting Microsoft Forms VM deployment process"
    Write-Log "Deployment ID: $DeploymentId"
    
    # Generate deployment ID if not provided
    if ([string]::IsNullOrEmpty($DeploymentId)) {
        $DeploymentId = "DEP-$(Get-Date -Format 'yyyyMMddHHmmss')-$((New-Guid).ToString().Substring(0,8).ToUpper())"
        Write-Log "Generated Deployment ID: $DeploymentId"
    }
    
    # Normalize input parameters
    $HospitalCode = $HospitalCode.ToUpper().Trim()
    $DepartmentCode = $DepartmentCode.ToUpper().Trim()
    $FirstName = $FirstName.Trim()
    $LastName = $LastName.Trim()
    
    Write-Log "Processing request for: $FirstName $LastName ($HospitalCode-$DepartmentCode)"
    
    # Validate input parameters
    $validationErrors = Test-InputValidation -Hospital $HospitalCode -Department $DepartmentCode -First $FirstName -Last $LastName
    
    if ($validationErrors.Count -gt 0) {
        Write-Log "Validation failed:" "ERROR"
        foreach ($error in $validationErrors) {
            Write-Log "  - $error" "ERROR"
        }
        throw "Input validation failed. Please check the parameters and try again."
    }
    
    # Generate VM name
    $VMName = New-VMName -Hospital $HospitalCode -Department $DepartmentCode -First $FirstName -Last $LastName -Type $VMType
    Write-Log "Generated VM name: $VMName"
    
    # Prepare parameters for Terraform
    $terraformParams = @{
        VMName = $VMName
        VMCount = $VMCount
        HospitalCode = $HospitalCode
        DepartmentCode = $DepartmentCode
        FirstName = $FirstName
        LastName = $LastName
        VMType = $VMType
        DeploymentId = $DeploymentId
        ResourceGroup = $ResourceGroup
        VMSize = $VMSize
        Location = $Location
        AdminUsername = $AdminUsername
        AdminPassword = $AdminPassword
        AzureDevOpsOrg = $AzureDevOpsOrg
        TeamProject = $TeamProject
        DeploymentGroup = $DeploymentGroup
        AzureDevOpsPat = $AzureDevOpsPat
        AgentFolder = $AgentFolder
    }
    
    # Create Terraform variables file
    $varsFile = New-TerraformVarsFile -VMName $VMName -Parameters $terraformParams
    
    # Change to Terraform directory
    $terraformDir = ".\01-Terraform-Core"
    if (-not (Test-Path $terraformDir)) {
        throw "Terraform directory not found: $terraformDir"
    }
    
    Push-Location $terraformDir
    
    try {
        Write-Log "Initializing Terraform..."
        terraform init
        
        if ($LASTEXITCODE -ne 0) {
            throw "Terraform init failed"
        }
        
        Write-Log "Running Terraform $Action..."
        
        switch ($Action) {
            "plan" {
                terraform plan -var-file="..\$varsFile" -out="tfplan-$DeploymentId"
            }
            "apply" {
                if ($AutoApprove) {
                    terraform apply -var-file="..\$varsFile" -auto-approve
                } else {
                    terraform apply -var-file="..\$varsFile"
                }
            }
            "destroy" {
                if ($AutoApprove) {
                    terraform destroy -var-file="..\$varsFile" -auto-approve
                } else {
                    terraform destroy -var-file="..\$varsFile"
                }
            }
        }
        
        if ($LASTEXITCODE -ne 0) {
            throw "Terraform $Action failed"
        }
        
        Write-Log "Terraform $Action completed successfully"
        
        # Generate deployment summary
        $summary = @{
            DeploymentId = $DeploymentId
            VMName = $VMName
            VMCount = $VMCount
            HospitalCode = $HospitalCode
            DepartmentCode = $DepartmentCode
            RequestedBy = "$FirstName $LastName"
            VMType = $VMType
            Action = $Action
            Status = "Success"
            CompletedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
        
        Write-Log "Deployment Summary:"
        $summary.GetEnumerator() | ForEach-Object {
            Write-Log "  $($_.Key): $($_.Value)"
        }
        
        return $summary
        
    } finally {
        Pop-Location
    }
    
} catch {
    Write-Log "Deployment failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    
    # Return error summary
    $errorSummary = @{
        DeploymentId = $DeploymentId
        VMName = $VMName
        Status = "Failed"
        Error = $_.Exception.Message
        FailedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    
    return $errorSummary
}
