<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure VM Deployment - Auto Deploy</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0078d4;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus, select:focus, textarea:focus {
            border-color: #0078d4;
            outline: none;
        }
        .form-row {
            display: flex;
            gap: 20px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .button-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 30px;
        }
        button {
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #0078d4;
            color: white;
        }
        .btn-primary:hover {
            background-color: #106ebe;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .required {
            color: red;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .section-title {
            background-color: #f8f9fa;
            padding: 10px;
            margin: 20px 0 10px 0;
            border-left: 4px solid #0078d4;
            font-weight: bold;
        }
        .deployment-status {
            margin-top: 20px;
            padding: 20px;
            border-radius: 5px;
            display: none;
        }
        .status-starting {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status-running {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status-completed {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-failed {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .logs-container {
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #0078d4;
            transition: width 0.3s ease;
            width: 0%;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0078d4;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .auto-deploy-notice {
            background-color: #e7f3ff;
            border: 2px solid #0078d4;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Azure VM Deployment - Auto Deploy</h1>
        
        <div class="auto-deploy-notice">
            <strong>⚡ Automatic Deployment Enabled!</strong><br>
            Fill out the form and click deploy - your VM will be created automatically without manual intervention.
        </div>

        <form id="vmForm">
            <!-- Basic VM Configuration -->
            <div class="section-title">Basic VM Configuration</div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="vmName">VM Name <span class="required">*</span></label>
                    <input type="text" id="vmName" name="vmName" value="CCH9APPVMCT" required>
                    <div class="help-text">Enter the base name for your VM(s)</div>
                </div>
                <div class="form-group">
                    <label for="vmCount">VM Count <span class="required">*</span></label>
                    <input type="number" id="vmCount" name="vmCount" value="1" min="1" max="10" required>
                    <div class="help-text">Number of VMs to create (1-10)</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="vmSize">VM Size <span class="required">*</span></label>
                    <select id="vmSize" name="vmSize" required>
                        <option value="Standard_B2s">Standard_B2s (2 vCPUs, 4GB RAM)</option>
                        <option value="Standard_D2s_v3">Standard_D2s_v3 (2 vCPUs, 8GB RAM)</option>
                        <option value="Standard_D4s_v3" selected>Standard_D4s_v3 (4 vCPUs, 16GB RAM)</option>
                        <option value="Standard_D8s_v3">Standard_D8s_v3 (8 vCPUs, 32GB RAM)</option>
                        <option value="Standard_E4s_v3">Standard_E4s_v3 (4 vCPUs, 32GB RAM)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="resourceGroup">Resource Group <span class="required">*</span></label>
                    <input type="text" id="resourceGroup" name="resourceGroup" value="MAH9-SUP-CCH9-VMC" required>
                </div>
            </div>

            <!-- OS Configuration -->
            <div class="section-title">Operating System Configuration</div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="vmSku">Windows Version <span class="required">*</span></label>
                    <select id="vmSku" name="vmSku" required>
                        <option value="2019-datacenter">Windows Server 2019 Datacenter</option>
                        <option value="2022-datacenter" selected>Windows Server 2022 Datacenter</option>
                        <option value="2022-datacenter-azure-edition">Windows Server 2022 Datacenter Azure Edition</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="location">Location <span class="required">*</span></label>
                    <select id="location" name="location" required>
                        <option value="East US">East US</option>
                        <option value="East US 2" selected>East US 2</option>
                        <option value="West US">West US</option>
                        <option value="West US 2">West US 2</option>
                        <option value="Central US">Central US</option>
                    </select>
                </div>
            </div>

            <!-- Admin Credentials -->
            <div class="section-title">Administrator Credentials</div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="adminUsername">Admin Username <span class="required">*</span></label>
                    <input type="text" id="adminUsername" name="adminUsername" value="maoperator" required>
                </div>
                <div class="form-group">
                    <label for="adminPassword">Admin Password <span class="required">*</span></label>
                    <input type="password" id="adminPassword" name="adminPassword" value="M1t1g@t0r2025" required>
                    <div class="help-text">Password must be 12-72 characters with uppercase, lowercase, number, and special character</div>
                </div>
            </div>

            <!-- Azure DevOps Configuration -->
            <div class="section-title">Azure DevOps Configuration</div>
            
            <div class="form-group">
                <label for="azureDevOpsOrg">Azure DevOps Organization URL <span class="required">*</span></label>
                <input type="url" id="azureDevOpsOrg" name="azureDevOpsOrg" value="https://dev.azure.com/MAHealth" required>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="teamProject">Team Project <span class="required">*</span></label>
                    <input type="text" id="teamProject" name="teamProject" value="MAH9-SCP-CCH9GI" required>
                </div>
                <div class="form-group">
                    <label for="deploymentGroup">Deployment Group <span class="required">*</span></label>
                    <input type="text" id="deploymentGroup" name="deploymentGroup" value="MAH9-SCP-CCH9-DGR-DEV" required>
                </div>
            </div>

            <div class="form-group">
                <label for="azureDevOpsPat">Azure DevOps PAT Token <span class="required">*</span></label>
                <input type="password" id="azureDevOpsPat" name="azureDevOpsPat" value="4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7" required>
                <div class="help-text">Personal Access Token with Agent Pools (read, manage) and Deployment Groups (read, manage) permissions</div>
            </div>

            <div class="form-group">
                <label for="agentFolder">Agent Installation Folder</label>
                <input type="text" id="agentFolder" name="agentFolder" value="c:/Agent">
            </div>

            <!-- Action Buttons -->
            <div class="button-group">
                <button type="button" class="btn-primary" onclick="deployVM('plan')">🔍 Plan Deployment</button>
                <button type="button" class="btn-success" onclick="deployVM('apply')">🚀 Deploy VM Now</button>
                <button type="button" class="btn-danger" onclick="deployVM('destroy')">🗑️ Destroy Infrastructure</button>
            </div>
        </form>

        <!-- Deployment Status -->
        <div id="deploymentStatus" class="deployment-status">
            <div id="statusHeader">
                <span id="statusIcon"></span>
                <strong id="statusTitle">Deployment Status</strong>
            </div>
            <div id="statusMessage">Ready to deploy</div>
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="deploymentDetails"></div>
            <div id="logsContainer" class="logs-container" style="display: none;">
                <div id="logs"></div>
            </div>
            <button id="showLogsBtn" onclick="toggleLogs()" style="margin-top: 10px; display: none;">Show Logs</button>
        </div>
    </div>

    <script>
        let currentDeploymentId = null;
        let statusCheckInterval = null;

        async function deployVM(action) {
            const form = document.getElementById('vmForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (action === 'destroy' && !confirm('⚠️ WARNING: This will destroy all infrastructure. Are you sure?')) {
                return;
            }

            if (action === 'apply' && !confirm('This will create real Azure resources and may incur costs. Continue?')) {
                return;
            }

            const formData = new FormData(form);

            // Show deployment status
            showDeploymentStatus('starting', 'Generating deployment script...');

            // Generate terraform.tfvars content
            const tfvarsContent = generateTerraformVars(formData);

            // Generate PowerShell script
            const psScript = generateDeploymentScript(action, formData);

            // Generate batch file
            const batScript = generateBatchScript(action, formData);

            // Show completion status
            showDeploymentStatus('completed', 'Deployment scripts generated successfully!');

            // Update deployment details
            updateDeploymentDetails({
                vmName: formData.get('vmName'),
                action: action,
                duration: 'Instant',
                lastUpdate: new Date().toISOString()
            });

            // Auto-download the scripts
            downloadFile('terraform.tfvars', tfvarsContent);
            setTimeout(() => downloadFile(`deploy-${action}-${formData.get('vmName')}.ps1`, psScript), 500);
            setTimeout(() => downloadFile(`deploy-${action}-${formData.get('vmName')}.bat`, batScript), 1000);

            // Show instructions
            setTimeout(() => {
                const instructions = `
🎉 Deployment scripts generated successfully!

📥 Downloaded files:
1. terraform.tfvars - Terraform configuration
2. deploy-${action}-${formData.get('vmName')}.ps1 - PowerShell script
3. deploy-${action}-${formData.get('vmName')}.bat - Batch file (easiest to use)

🚀 To deploy your VM:
Option 1 (Easiest): Double-click the .bat file
Option 2: Run the .ps1 file in PowerShell
Option 3: Copy terraform.tfvars to your Terraform directory and run terraform ${action}

⚡ The batch file will automatically:
✅ Create terraform.tfvars in the right location
✅ Run terraform init, validate, and ${action}
✅ Deploy your VM completely automatically
✅ Show real-time progress

Just double-click the .bat file and your VM will be deployed! 🚀
                `;

                document.getElementById('logsContainer').style.display = 'block';
                document.getElementById('logs').innerHTML = `<pre style="white-space: pre-wrap; font-family: 'Segoe UI', sans-serif;">${instructions}</pre>`;
                document.getElementById('showLogsBtn').style.display = 'inline-block';
                document.getElementById('showLogsBtn').textContent = 'Show Instructions';
            }, 1500);
        }

        function showDeploymentStatus(status, message) {
            const statusDiv = document.getElementById('deploymentStatus');
            const statusIcon = document.getElementById('statusIcon');
            const statusTitle = document.getElementById('statusTitle');
            const statusMessage = document.getElementById('statusMessage');
            const progressFill = document.getElementById('progressFill');

            statusDiv.className = `deployment-status status-${status}`;
            statusDiv.style.display = 'block';
            statusMessage.textContent = message;

            switch (status) {
                case 'starting':
                    statusIcon.innerHTML = '<div class="spinner"></div>';
                    statusTitle.textContent = 'Starting Deployment';
                    progressFill.style.width = '10%';
                    break;
                case 'running':
                    statusIcon.innerHTML = '<div class="spinner"></div>';
                    statusTitle.textContent = 'Deployment Running';
                    progressFill.style.width = '50%';
                    break;
                case 'completed':
                    statusIcon.innerHTML = '✅';
                    statusTitle.textContent = 'Deployment Completed';
                    progressFill.style.width = '100%';
                    break;
                case 'failed':
                    statusIcon.innerHTML = '❌';
                    statusTitle.textContent = 'Deployment Failed';
                    progressFill.style.width = '100%';
                    break;
            }
        }

        function startStatusChecking() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(async () => {
                if (!currentDeploymentId) return;

                try {
                    const response = await fetch(`/api/status/${currentDeploymentId}`);
                    const status = await response.json();

                    showDeploymentStatus(status.status, status.message);
                    updateDeploymentDetails(status);

                    if (status.status === 'completed' || status.status === 'failed') {
                        clearInterval(statusCheckInterval);
                        statusCheckInterval = null;
                        
                        if (status.logs && status.logs.length > 0) {
                            document.getElementById('showLogsBtn').style.display = 'inline-block';
                        }
                    }
                } catch (error) {
                    console.error('Error checking status:', error);
                }
            }, 2000); // Check every 2 seconds
        }

        function updateDeploymentDetails(status) {
            const detailsDiv = document.getElementById('deploymentDetails');
            const duration = status.duration ? `${status.duration}s` : 'In progress...';
            
            detailsDiv.innerHTML = `
                <div style="margin-top: 10px; font-size: 14px;">
                    <strong>VM Name:</strong> ${status.vmName}<br>
                    <strong>Action:</strong> ${status.action}<br>
                    <strong>Duration:</strong> ${duration}<br>
                    <strong>Last Update:</strong> ${new Date(status.lastUpdate).toLocaleTimeString()}
                </div>
            `;

            // Update logs in real-time
            if (status.logs && status.logs.length > 0) {
                const logsDiv = document.getElementById('logs');
                logsDiv.innerHTML = status.logs.map(log => 
                    `<div class="log-entry">[${new Date(log.timestamp).toLocaleTimeString()}] ${log.message}</div>`
                ).join('');
                
                // Auto-scroll to bottom
                const logsContainer = document.getElementById('logsContainer');
                if (logsContainer.style.display !== 'none') {
                    logsContainer.scrollTop = logsContainer.scrollHeight;
                }
            }
        }

        function toggleLogs() {
            const logsContainer = document.getElementById('logsContainer');
            const btn = document.getElementById('showLogsBtn');
            
            if (logsContainer.style.display === 'none') {
                logsContainer.style.display = 'block';
                btn.textContent = 'Hide Logs';
                logsContainer.scrollTop = logsContainer.scrollHeight;
            } else {
                logsContainer.style.display = 'none';
                btn.textContent = 'Show Logs';
            }
        }

        function generateTerraformVars(formData) {
            return `vm_name = "${formData.get('vmName')}"
vm_count = ${formData.get('vmCount')}
vm_resourcegroup = "${formData.get('resourceGroup')}"
vm_size = "${formData.get('vmSize')}"
vm_sku = "${formData.get('vmSku')}"
vm_location = "${formData.get('location')}"
vm_admin_username = "${formData.get('adminUsername')}"
vm_admin_password = "${formData.get('adminPassword')}"
azure_devops_organization = "${formData.get('azureDevOpsOrg')}"
azure_devops_teamproject = "${formData.get('teamProject')}"
azure_devops_deploymentgroup = "${formData.get('deploymentGroup')}"
azure_devops_pat = "${formData.get('azureDevOpsPat')}"
azure_devops_agentfolder = "${formData.get('agentFolder')}"`;
        }

        function generateDeploymentScript(action, formData) {
            const vmName = formData.get('vmName');
            const vmCount = formData.get('vmCount');

            return `# Auto-Generated Deployment Script
# VM: ${vmName}
# Action: ${action}
# Generated: ${new Date().toISOString()}

param(
    [string]$TerraformDir = "D:\\\\Maspects\\\\MAHealth\\\\Terraform\\\\Scope_Demo-Environment"
)

Clear-Host
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🚀 Auto-Deploy: ${vmName}" -ForegroundColor Cyan
Write-Host "    Action: ${action.toUpperCase()}" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to Terraform directory
if (Test-Path $TerraformDir) {
    Set-Location $TerraformDir
    Write-Host "✅ Changed to Terraform directory" -ForegroundColor Green
} else {
    Write-Host "❌ Terraform directory not found: $TerraformDir" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check prerequisites
if (!(Test-Path "main.tf")) {
    Write-Host "❌ main.tf not found in current directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Terraform not found in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Prerequisites checked" -ForegroundColor Green
Write-Host ""

try {
    # Create terraform.tfvars
    Write-Host "📝 Creating terraform.tfvars..." -ForegroundColor Cyan

    $tfvarsContent = @"
vm_name = "${vmName}"
vm_count = ${vmCount}
vm_resourcegroup = "${formData.get('resourceGroup')}"
vm_size = "${formData.get('vmSize')}"
vm_sku = "${formData.get('vmSku')}"
vm_location = "${formData.get('location')}"
vm_admin_username = "${formData.get('adminUsername')}"
vm_admin_password = "${formData.get('adminPassword')}"
azure_devops_organization = "${formData.get('azureDevOpsOrg')}"
azure_devops_teamproject = "${formData.get('teamProject')}"
azure_devops_deploymentgroup = "${formData.get('deploymentGroup')}"
azure_devops_pat = "${formData.get('azureDevOpsPat')}"
azure_devops_agentfolder = "${formData.get('agentFolder')}"
"@

    $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
    Write-Host "✅ terraform.tfvars created" -ForegroundColor Green

    # Initialize Terraform
    Write-Host "🔧 Initializing Terraform..." -ForegroundColor Cyan
    terraform init
    if ($LASTEXITCODE -ne 0) { throw "Terraform init failed" }
    Write-Host "✅ Terraform initialized" -ForegroundColor Green

    # Validate
    Write-Host "🔍 Validating configuration..." -ForegroundColor Cyan
    terraform validate
    if ($LASTEXITCODE -ne 0) { throw "Terraform validation failed" }
    Write-Host "✅ Configuration validated" -ForegroundColor Green

    # Execute action
    Write-Host "🚀 Running terraform ${action}..." -ForegroundColor Cyan
    ${action === 'apply' ? 'Write-Host "This may take 10-15 minutes..." -ForegroundColor Yellow' : ''}

    terraform ${action} -var-file="terraform.tfvars"${action === 'apply' || action === 'destroy' ? ' -auto-approve' : ''}
    if ($LASTEXITCODE -ne 0) { throw "Terraform ${action} failed" }

    Write-Host ""
    Write-Host "🎉 Terraform ${action} completed successfully!" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "❌ Deployment failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Process completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "Press Enter to exit"`;
        }

        function generateBatchScript(action, formData) {
            const vmName = formData.get('vmName');
            const scriptName = `deploy-${action}-${vmName}.ps1`;

            return `@echo off
echo ========================================
echo    🚀 Auto-Deploy: ${vmName}
echo    Action: ${action.toUpperCase()}
echo ========================================
echo.
echo This will automatically ${action} your VM!
echo.
pause

REM Run the PowerShell deployment script
powershell.exe -ExecutionPolicy Bypass -File "${scriptName}"

echo.
echo Deployment process completed!
pause`;
        }

        function downloadFile(filename, content) {
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Form validation
        document.getElementById('adminPassword').addEventListener('input', function(e) {
            const password = e.target.value;
            const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,72}$/;
            
            if (password && !regex.test(password)) {
                e.target.setCustomValidity('Password must be 12-72 characters with uppercase, lowercase, number, and special character');
            } else {
                e.target.setCustomValidity('');
            }
        });

        // Show ready status on page load
        window.addEventListener('load', () => {
            // Show ready status
            const statusInfo = document.createElement('div');
            statusInfo.style.cssText = 'position: fixed; top: 10px; right: 10px; background: #d4edda; padding: 10px; border-radius: 5px; font-size: 12px; z-index: 1000; color: #155724;';
            statusInfo.innerHTML = `🟢 Form Ready<br>Mode: Script Generator<br>Status: Online`;
            document.body.appendChild(statusInfo);
        });
    </script>
</body>
</html>
