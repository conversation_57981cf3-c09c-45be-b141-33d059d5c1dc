<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Azure VM Deployment Form</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0078d4;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus, select:focus, textarea:focus {
            border-color: #0078d4;
            outline: none;
        }
        .form-row {
            display: flex;
            gap: 20px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .button-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 30px;
        }
        button {
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #0078d4;
            color: white;
        }
        .btn-primary:hover {
            background-color: #106ebe;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .required {
            color: red;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        #output {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #0078d4;
            display: none;
        }
        .section-title {
            background-color: #f8f9fa;
            padding: 10px;
            margin: 20px 0 10px 0;
            border-left: 4px solid #0078d4;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Azure VM Deployment Form</h1>
        <form id="vmForm">
            <!-- Basic VM Configuration -->
            <div class="section-title">Basic VM Configuration</div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="vmName">VM Name <span class="required">*</span></label>
                    <input type="text" id="vmName" name="vmName" value="CCH9APPVMCT" required>
                    <div class="help-text">Enter the base name for your VM(s)</div>
                </div>
                <div class="form-group">
                    <label for="vmCount">VM Count <span class="required">*</span></label>
                    <input type="number" id="vmCount" name="vmCount" value="1" min="1" max="10" required>
                    <div class="help-text">Number of VMs to create (1-10)</div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="vmSize">VM Size <span class="required">*</span></label>
                    <select id="vmSize" name="vmSize" required>
                        <option value="Standard_B2s">Standard_B2s (2 vCPUs, 4GB RAM)</option>
                        <option value="Standard_D2s_v3">Standard_D2s_v3 (2 vCPUs, 8GB RAM)</option>
                        <option value="Standard_D4s_v3" selected>Standard_D4s_v3 (4 vCPUs, 16GB RAM)</option>
                        <option value="Standard_D8s_v3">Standard_D8s_v3 (8 vCPUs, 32GB RAM)</option>
                        <option value="Standard_E4s_v3">Standard_E4s_v3 (4 vCPUs, 32GB RAM)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="resourceGroup">Resource Group <span class="required">*</span></label>
                    <input type="text" id="resourceGroup" name="resourceGroup" value="MAH9-SUP-CCH9-VMC" required>
                </div>
            </div>

            <!-- OS Configuration -->
            <div class="section-title">Operating System Configuration</div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="vmSku">Windows Version <span class="required">*</span></label>
                    <select id="vmSku" name="vmSku" required>
                        <option value="2019-datacenter">Windows Server 2019 Datacenter</option>
                        <option value="2022-datacenter" selected>Windows Server 2022 Datacenter</option>
                        <option value="2022-datacenter-azure-edition">Windows Server 2022 Datacenter Azure Edition</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="location">Location <span class="required">*</span></label>
                    <select id="location" name="location" required>
                        <option value="East US">East US</option>
                        <option value="East US 2" selected>East US 2</option>
                        <option value="West US">West US</option>
                        <option value="West US 2">West US 2</option>
                        <option value="Central US">Central US</option>
                    </select>
                </div>
            </div>

            <!-- Admin Credentials -->
            <div class="section-title">Administrator Credentials</div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="adminUsername">Admin Username <span class="required">*</span></label>
                    <input type="text" id="adminUsername" name="adminUsername" value="maoperator" required>
                </div>
                <div class="form-group">
                    <label for="adminPassword">Admin Password <span class="required">*</span></label>
                    <input type="password" id="adminPassword" name="adminPassword" required>
                    <div class="help-text">Password must be 12-72 characters with uppercase, lowercase, number, and special character</div>
                </div>
            </div>

            <!-- Azure DevOps Configuration -->
            <div class="section-title">Azure DevOps Configuration</div>
            
            <div class="form-group">
                <label for="azureDevOpsOrg">Azure DevOps Organization URL <span class="required">*</span></label>
                <input type="url" id="azureDevOpsOrg" name="azureDevOpsOrg" value="https://dev.azure.com/MAHealth" required>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="teamProject">Team Project <span class="required">*</span></label>
                    <input type="text" id="teamProject" name="teamProject" value="MAH9-SCP-CCH9GI" required>
                </div>
                <div class="form-group">
                    <label for="deploymentGroup">Deployment Group <span class="required">*</span></label>
                    <input type="text" id="deploymentGroup" name="deploymentGroup" value="MAH9-SCP-CCH9-DGR-DEV" required>
                </div>
            </div>

            <div class="form-group">
                <label for="azureDevOpsPat">Azure DevOps PAT Token <span class="required">*</span></label>
                <input type="password" id="azureDevOpsPat" name="azureDevOpsPat" required>
                <div class="help-text">Personal Access Token with Agent Pools (read, manage) and Deployment Groups (read, manage) permissions</div>
            </div>

            <div class="form-group">
                <label for="agentFolder">Agent Installation Folder</label>
                <input type="text" id="agentFolder" name="agentFolder" value="c:/Agent">
            </div>

            <!-- Action Buttons -->
            <div class="button-group">
                <button type="button" class="btn-secondary" onclick="generateTerraformVars()">📋 Generate terraform.tfvars</button>
                <button type="button" class="btn-primary" onclick="planDeployment()">🔍 Plan Deployment</button>
                <button type="button" class="btn-success" onclick="deployInfrastructure()">🚀 Deploy Infrastructure</button>
                <button type="button" class="btn-secondary" onclick="downloadScript('plan')">📥 Download Plan Script</button>
                <button type="button" class="btn-success" onclick="downloadScript('apply')">📥 Download Deploy Script</button>
            </div>

            <!-- Quick Actions -->
            <div class="button-group" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #ddd;">
                <button type="button" class="btn-primary" onclick="executeDirectly('plan')" style="background-color: #17a2b8;">⚡ Execute Plan Now</button>
                <button type="button" class="btn-success" onclick="executeDirectly('apply')" style="background-color: #28a745;">⚡ Execute Deploy Now</button>
                <button type="button" class="btn-secondary" onclick="executeDirectly('destroy')" style="background-color: #dc3545; color: white;">🗑️ Destroy Infrastructure</button>
            </div>

            <div style="margin-top: 10px; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                <small><strong>Note:</strong> "Execute Now" buttons will generate PowerShell scripts that you need to run manually. Browser security prevents direct Terraform execution.</small>
            </div>
        </form>

        <div id="output"></div>
    </div>

    <script>
        function generateTerraformVars() {
            const form = document.getElementById('vmForm');
            const formData = new FormData(form);
            
            let tfvars = `# Generated terraform.tfvars file
# Generated on: ${new Date().toISOString()}

vm_name = "${formData.get('vmName')}"
vm_count = ${formData.get('vmCount')}
vm_resourcegroup = "${formData.get('resourceGroup')}"
vm_size = "${formData.get('vmSize')}"
vm_sku = "${formData.get('vmSku')}"
vm_location = "${formData.get('location')}"
vm_admin_username = "${formData.get('adminUsername')}"
vm_admin_password = "${formData.get('adminPassword')}"
azure_devops_organization = "${formData.get('azureDevOpsOrg')}"
azure_devops_teamproject = "${formData.get('teamProject')}"
azure_devops_deploymentgroup = "${formData.get('deploymentGroup')}"
azure_devops_pat = "${formData.get('azureDevOpsPat')}"
azure_devops_agentfolder = "${formData.get('agentFolder')}"
`;

            showOutput('Generated terraform.tfvars:', tfvars);
            
            // Download the file
            const blob = new Blob([tfvars], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'terraform.tfvars';
            a.click();
            window.URL.revokeObjectURL(url);
        }

        function planDeployment() {
            const form = document.getElementById('vmForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            const powerShellCommand = generatePowerShellCommand('plan', formData);
            
            showOutput('PowerShell Command for Planning:', powerShellCommand);
            
            // You can integrate with Power Automate here
            callPowerAutomate('plan', formData);
        }

        function deployInfrastructure() {
            const form = document.getElementById('vmForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (!confirm('Are you sure you want to deploy the infrastructure? This will create real Azure resources.')) {
                return;
            }

            const formData = new FormData(form);
            const powerShellCommand = generatePowerShellCommand('apply', formData);
            
            showOutput('PowerShell Command for Deployment:', powerShellCommand);
            
            // You can integrate with Power Automate here
            callPowerAutomate('apply', formData);
        }

        function generatePowerShellCommand(action, formData) {
            return `# PowerShell command to ${action} Terraform
$params = @{
    Action = "${action}"
    VmName = "${formData.get('vmName')}"
    VmCount = ${formData.get('vmCount')}
    ResourceGroupName = "${formData.get('resourceGroup')}"
    VmSize = "${formData.get('vmSize')}"
    VmSku = "${formData.get('vmSku')}"
    Location = "${formData.get('location')}"
    AdminUsername = "${formData.get('adminUsername')}"
    AdminPassword = "${formData.get('adminPassword')}"
    AzureDevOpsOrg = "${formData.get('azureDevOpsOrg')}"
    TeamProject = "${formData.get('teamProject')}"
    DeploymentGroup = "${formData.get('deploymentGroup')}"
    AzureDevOpsPat = "${formData.get('azureDevOpsPat')}"
    AgentFolder = "${formData.get('agentFolder')}"
}

.\\Deploy-Terraform-PowerAutomate.ps1 @params`;
        }

        async function callPowerAutomate(action, formData) {
            showOutput('Preparing deployment...', 'Please wait while we prepare your VM deployment.');

            // Create the PowerShell command
            const psCommand = generatePowerShellCommand(action, formData);

            // Show the command to the user
            showOutput('PowerShell Command Generated:', psCommand + '\n\n⚠️ IMPORTANT: Copy and run this command in PowerShell in your Terraform directory to deploy the VM.');

            // Try to execute via local PowerShell (this will only work if running locally)
            try {
                await executeLocalPowerShell(action, formData);
            } catch (error) {
                showOutput('Local Execution Not Available',
                    'Local PowerShell execution is not available in the browser.\n\n' +
                    '📋 NEXT STEPS:\n' +
                    '1. Copy the PowerShell command above\n' +
                    '2. Open PowerShell as Administrator\n' +
                    '3. Navigate to your Terraform directory\n' +
                    '4. Paste and run the command\n\n' +
                    'Alternative: Use the "Download Script" button below to save the command as a .ps1 file.'
                );

                // Create downloadable PowerShell script
                createDownloadableScript(action, formData);
            }
        }

        async function executeLocalPowerShell(action, formData) {
            // This function attempts to execute PowerShell locally
            // Note: This requires the page to be served from a local server with appropriate permissions

            const scriptContent = generatePowerShellScript(action, formData);

            // Create a blob with the PowerShell script
            const blob = new Blob([scriptContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);

            // Create download link for the script
            const a = document.createElement('a');
            a.href = url;
            a.download = `deploy-vm-${action}-${formData.get('vmName')}.ps1`;
            a.textContent = '📥 Download PowerShell Script';
            a.className = 'btn-primary';
            a.style.display = 'inline-block';
            a.style.margin = '10px 0';

            const output = document.getElementById('output');
            output.appendChild(document.createElement('br'));
            output.appendChild(a);

            window.URL.revokeObjectURL(url);
        }

        function createDownloadableScript(action, formData) {
            const scriptContent = generatePowerShellScript(action, formData);

            const blob = new Blob([scriptContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `deploy-vm-${action}-${formData.get('vmName')}.ps1`;
            a.click();
            window.URL.revokeObjectURL(url);
        }

        function generatePowerShellScript(action, formData) {
            return `# VM Deployment Script - Generated on ${new Date().toISOString()}
# Action: ${action}
# VM Name: ${formData.get('vmName')}

# Navigate to Terraform directory (update this path as needed)
Set-Location "D:\\Maspects\\MAHealth\\Terraform\\Scope_Demo-Environment"

# Execute the deployment
.\\Deploy-VM-Terraform.ps1 \`
    -Action "${action}" \`
    -VmName "${formData.get('vmName')}" \`
    -VmCount ${formData.get('vmCount')} \`
    -ResourceGroupName "${formData.get('resourceGroup')}" \`
    -VmSize "${formData.get('vmSize')}" \`
    -VmSku "${formData.get('vmSku')}" \`
    -Location "${formData.get('location')}" \`
    -AdminUsername "${formData.get('adminUsername')}" \`
    -AdminPassword "${formData.get('adminPassword')}" \`
    -AzureDevOpsOrg "${formData.get('azureDevOpsOrg')}" \`
    -TeamProject "${formData.get('teamProject')}" \`
    -DeploymentGroup "${formData.get('deploymentGroup')}" \`
    -AzureDevOpsPat "${formData.get('azureDevOpsPat')}" \`
    -AgentFolder "${formData.get('agentFolder')}" \`
    -AutoApprove

Write-Host "Deployment completed. Check the output above for results."
Read-Host "Press Enter to exit"`;
        }

        function showOutput(title, content) {
            const output = document.getElementById('output');
            output.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            output.style.display = 'block';
            output.scrollIntoView({ behavior: 'smooth' });
        }

        function downloadScript(action) {
            const form = document.getElementById('vmForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            createDownloadableScript(action, formData);
            showOutput(`${action.toUpperCase()} Script Downloaded`, `PowerShell script for ${action} has been downloaded. Run it in your Terraform directory.`);
        }

        function executeDirectly(action) {
            const form = document.getElementById('vmForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (action === 'destroy' && !confirm('⚠️ WARNING: This will destroy all infrastructure created by this configuration. Are you sure?')) {
                return;
            }

            if (action === 'apply' && !confirm('This will create real Azure resources and may incur costs. Continue?')) {
                return;
            }

            const formData = new FormData(form);

            // Show immediate feedback
            showOutput(`Preparing ${action.toUpperCase()} execution...`, 'Generating PowerShell script for execution...');

            // Generate and show the command
            const psCommand = generatePowerShellCommand(action, formData);

            setTimeout(() => {
                showOutput(`${action.toUpperCase()} Command Ready`,
                    `Copy and run this command in PowerShell (as Administrator) in your Terraform directory:\n\n${psCommand}\n\n` +
                    `📁 Make sure you're in: D:\\Maspects\\MAHealth\\Terraform\\Scope_Demo-Environment\n\n` +
                    `⚡ Or click the download button to save as a .ps1 file.`
                );

                // Auto-download the script
                createDownloadableScript(action, formData);
            }, 500);
        }

        // Form validation
        document.getElementById('adminPassword').addEventListener('input', function(e) {
            const password = e.target.value;
            const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,72}$/;

            if (password && !regex.test(password)) {
                e.target.setCustomValidity('Password must be 12-72 characters with uppercase, lowercase, number, and special character');
            } else {
                e.target.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
