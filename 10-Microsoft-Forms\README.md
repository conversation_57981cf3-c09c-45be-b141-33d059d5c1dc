# Microsoft Forms VM Deployment Automation

## Overview

This solution provides a fully automated VM deployment workflow that integrates Microsoft Forms with Azure DevOps and Terraform. Hospital staff can request VMs by filling out a simple form, which automatically generates standardized VM names and triggers deployment without any manual intervention.

## 🏥 Business Requirements Addressed

- **Hospital Code**: 4-character hospital identifier (e.g., "CCH9")
- **Department Code**: 4-character department identifier (e.g., "SUPP")
- **User Information**: First and last name for VM naming
- **Automated VM Naming**: Generates names like `CCH9SUPPJDVMCT` (Hospital + Department + User Initials + VM + Type)
- **Zero Manual Intervention**: Form submission automatically triggers Terraform deployment

## 📁 Solution Components

### 1. Microsoft Forms Integration (`10-Microsoft-Forms/`)
- **`vm-request-form.html`**: Interactive web form with validation
- **`vm-name-generator.js`**: VM name generation and validation logic
- **`validation-and-error-handling.js`**: Comprehensive form validation
- **`power-automate-microsoft-forms-flow.json`**: Power Automate flow configuration

### 2. Terraform Configuration
- **`terraform-variables-forms.tf`**: Extended variables for form-driven deployments
- **`Deploy-VM-Microsoft-Forms.ps1`**: PowerShell script for form-based deployments

### 3. DevOps Integration
- **`azure-pipelines-forms.yml`**: Azure DevOps pipeline for automated deployment

## 🚀 Workflow Process

### Step-by-Step Process:

1. **Form Submission**: User fills out the Microsoft Form with:
   - Hospital Code (4 characters)
   - Department Code (4 characters)  
   - First Name and Last Name
   - VM Type and Count (optional)

2. **Validation**: Client-side and server-side validation ensures:
   - Hospital code is exactly 4 alphanumeric characters
   - Department code is exactly 4 letters
   - Names are valid and at least 2 characters
   - Generated VM name doesn't exceed Azure limits

3. **VM Name Generation**: Automatic generation using format:
   ```
   [HospitalCode][DeptCode][UserInitials]VM[Type]
   Example: CCH9SUPPJDVMCT
   ```

4. **Power Automate Processing**: 
   - Receives form submission
   - Validates data
   - Generates Terraform variables
   - Triggers Azure DevOps pipeline

5. **Terraform Deployment**:
   - Creates Azure resources
   - Configures VM with Azure DevOps agent
   - Applies proper tags and metadata

6. **Notifications**: Teams notifications sent for:
   - Deployment start
   - Deployment completion/failure
   - Status updates

## 🛠️ Setup Instructions

### Prerequisites

1. **Azure Subscription** with appropriate permissions
2. **Azure DevOps Organization** with:
   - Service connections configured
   - Variable groups for secrets
   - Terraform extension installed
3. **Microsoft Forms** license
4. **Power Automate** license
5. **Microsoft Teams** for notifications

### 1. Microsoft Forms Setup

1. Create a new Microsoft Form with these questions:
   - **Hospital Code**: Text input, required, 4 characters
   - **Department Code**: Text input, required, 4 characters
   - **First Name**: Text input, required
   - **Last Name**: Text input, required
   - **VM Type**: Choice (CT, DB, WB, AP, DC, FS, PS, TS)
   - **VM Count**: Number input, 1-5

2. Note the Form ID from the URL for Power Automate configuration

### 2. Power Automate Setup

1. Import the flow from `power-automate-microsoft-forms-flow.json`
2. Update the Form ID in the trigger
3. Configure connections:
   - Microsoft Forms
   - Microsoft Teams
   - Azure Automation
4. Update parameters:
   - Teams channel ID
   - Azure subscription details
   - Automation account information

### 3. Azure DevOps Setup

1. Create a new pipeline using `azure-pipelines-forms.yml`
2. Configure variable groups:
   ```yaml
   terraform-secrets:
     - azureServiceConnection
     - resourceGroup
     - vmSize
     - location
     - adminUsername
     - adminPassword
     - azureDevOpsOrg
     - teamProject
     - deploymentGroup
     - azureDevOpsPat
     - agentFolder
     - terraformStateResourceGroup
     - terraformStateStorageAccount
   ```
3. Set up service connections for Azure
4. Configure pipeline triggers (manual/API)

### 4. Terraform Configuration

1. Copy `terraform-variables-forms.tf` to your Terraform directory
2. Update `main.tf` to include the new variables
3. Configure backend for state management
4. Test with sample variables

## 📝 VM Naming Convention

### Format: `[HospitalCode][DeptCode][UserInitials]VM[Type]`

**Components:**
- **Hospital Code**: 4 alphanumeric characters (e.g., "CCH9", "MGH1")
- **Department Code**: 4 letters (e.g., "SUPP", "CARD", "EMER")
- **User Initials**: 2 letters from first and last name (e.g., "JD" for John Doe)
- **VM Prefix**: Always "VM"
- **Type Code**: 2 letters indicating VM purpose

**VM Types:**
- **CT**: Compute Server
- **DB**: Database Server
- **WB**: Web Server
- **AP**: Application Server
- **DC**: Domain Controller
- **FS**: File Server
- **PS**: Print Server
- **TS**: Terminal Server

**Examples:**
- `CCH9SUPPJDVMCT` - Cleveland Clinic Hospital 9, Supply Dept, John Doe, Compute VM
- `MGH1CARDJSVMDB` - Mass General Hospital 1, Cardiology, Jane Smith, Database VM
- `BWH2EMERJBVMWB` - Brigham Women's Hospital 2, Emergency, John Brown, Web VM

## 🔧 Configuration Options

### Form Validation Rules

```javascript
{
  hospitalCode: {
    required: true,
    length: 4,
    pattern: /^[A-Z0-9]{4}$/,
    message: 'Hospital code must be exactly 4 alphanumeric characters'
  },
  departmentCode: {
    required: true,
    length: 4,
    pattern: /^[A-Z]{4}$/,
    message: 'Department code must be exactly 4 letters'
  }
}
```

### Terraform Variables

Key variables that can be customized:

```hcl
variable "vm_size" {
  description = "Azure VM size/SKU"
  type        = string
  default     = "Standard_D4s_v3"
}

variable "vm_count" {
  description = "Number of VMs to create"
  type        = number
  default     = 1
  validation {
    condition     = var.vm_count >= 1 && var.vm_count <= 5
    error_message = "VM count must be between 1 and 5."
  }
}
```

## 🧪 Testing

### Test Scenarios

1. **Valid Form Submission**:
   ```
   Hospital Code: CCH9
   Department Code: SUPP
   First Name: John
   Last Name: Doe
   VM Type: CT
   VM Count: 1
   Expected VM Name: CCH9SUPPJDVMCT
   ```

2. **Multiple VMs**:
   ```
   Same as above with VM Count: 3
   Expected VM Names: CCH9SUPPJDVMCT01, CCH9SUPPJDVMCT02, CCH9SUPPJDVMCT03
   ```

3. **Validation Errors**:
   - Hospital code with 3 characters: Should fail
   - Department code with numbers: Should fail
   - Empty names: Should fail

### Manual Testing Steps

1. **Form Validation**:
   - Open `vm-request-form.html` in browser
   - Test various input combinations
   - Verify real-time validation works
   - Check VM name preview updates

2. **Power Automate Flow**:
   - Submit valid form
   - Check flow execution in Power Automate
   - Verify Teams notifications
   - Confirm pipeline trigger

3. **Pipeline Execution**:
   - Monitor Azure DevOps pipeline
   - Check Terraform plan output
   - Verify resource creation
   - Test deployment completion

## 🚨 Troubleshooting

### Common Issues

1. **Form Validation Fails**:
   - Check character limits
   - Verify pattern matching
   - Ensure all required fields filled

2. **Power Automate Flow Errors**:
   - Verify Form ID is correct
   - Check connection permissions
   - Validate variable mappings

3. **Pipeline Failures**:
   - Check service connection
   - Verify variable group values
   - Review Terraform state

4. **VM Name Conflicts**:
   - Check for existing VMs with same name
   - Verify naming convention compliance
   - Consider adding timestamp suffix

### Error Codes

- **VAL001**: Hospital code validation failed
- **VAL002**: Department code validation failed
- **VAL003**: User name validation failed
- **VAL004**: VM name length exceeded
- **DEP001**: Deployment initialization failed
- **DEP002**: Terraform execution failed
- **DEP003**: Resource creation failed

## 📞 Support

### Contact Information

- **IT Support**: For general VM requests and issues
- **DevOps Team**: For pipeline and deployment issues
- **Security Team**: For access and security concerns

### Escalation Process

1. **Level 1**: Form validation errors - User self-service
2. **Level 2**: Deployment failures - IT Support
3. **Level 3**: Infrastructure issues - DevOps Team
4. **Level 4**: Security incidents - Security Team

## 📋 Quick Reference

### Key Commands

```powershell
# Manual deployment
.\Deploy-VM-Microsoft-Forms.ps1 -HospitalCode "CCH9" -DepartmentCode "SUPP" -FirstName "John" -LastName "Doe" -AzureDevOpsPat "your-pat"

# Validation only
.\Deploy-VM-Microsoft-Forms.ps1 -Action "plan" [other parameters]

# Destroy resources
.\Deploy-VM-Microsoft-Forms.ps1 -Action "destroy" [other parameters]
```
