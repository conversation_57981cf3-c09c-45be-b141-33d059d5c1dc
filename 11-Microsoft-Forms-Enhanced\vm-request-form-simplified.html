<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏥 VM Request Form - Simplified</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-header h1 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .form-header p {
            color: #666;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 1em;
        }

        .required {
            color: #e74c3c;
        }

        input[type="text"],
        select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        input[type="text"]:focus,
        select:focus {
            outline: none;
            border-color: #667eea;
            background-color: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-hint {
            font-size: 0.85em;
            color: #666;
            margin-top: 5px;
        }

        .validation-message {
            font-size: 0.85em;
            margin-top: 5px;
            padding: 5px 10px;
            border-radius: 4px;
            display: none;
        }

        .validation-message.error {
            background-color: #fee;
            color: #c33;
            border: 1px solid #fcc;
            display: block;
        }

        .validation-message.success {
            background-color: #efe;
            color: #363;
            border: 1px solid #cfc;
            display: block;
        }

        .vm-preview {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        .vm-preview.show {
            display: block;
        }

        .vm-preview h3 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .vm-name-display {
            font-family: 'Courier New', monospace;
            font-size: 1.5em;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1em;
            font-weight: 600;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .info-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .info-section h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .info-section ul {
            margin-left: 20px;
            color: #666;
        }

        .info-section li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .form-container {
                padding: 20px;
                margin: 10px;
            }
            
            .form-header h1 {
                font-size: 1.8em;
            }
        }
    </style>
</head>
<body>
    <div class="form-container">
        <div class="form-header">
            <h1>🏥 VM Request Form</h1>
            <p>Automated Hospital VM Deployment</p>
        </div>

        <form id="vmRequestForm" action="#" method="post">
            <!-- Hospital Code -->
            <div class="form-group">
                <label for="hospitalCode">Hospital Name/Code <span class="required">*</span></label>
                <input type="text" id="hospitalCode" name="hospitalCode" maxlength="4" required 
                       placeholder="e.g., CCH9, MGH1" style="text-transform: uppercase;">
                <div class="input-hint">4 characters maximum (letters and numbers)</div>
                <div class="validation-message" id="hospitalCodeValidation"></div>
            </div>

            <!-- Department Code -->
            <div class="form-group">
                <label for="departmentCode">Department <span class="required">*</span></label>
                <input type="text" id="departmentCode" name="departmentCode" maxlength="4" required 
                       placeholder="e.g., SUPP, CARD, ICU1, ER2" style="text-transform: uppercase;">
                <div class="input-hint">4 characters maximum - letters and numbers (e.g., Supply = SUPP, ICU1 = ICU1)</div>
                <div class="validation-message" id="departmentCodeValidation"></div>
            </div>

            <!-- User Information (Optional) -->
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName">First Name (Optional)</label>
                    <input type="text" id="firstName" name="firstName" placeholder="John">
                    <div class="input-hint">Optional - for personalized VM naming</div>
                </div>
                <div class="form-group">
                    <label for="lastName">Last Name (Optional)</label>
                    <input type="text" id="lastName" name="lastName" placeholder="Doe">
                    <div class="input-hint">Optional - for personalized VM naming</div>
                </div>
            </div>

            <!-- VM Name Preview -->
            <div class="vm-preview" id="vmPreview">
                <h3>🖥️ Generated VM Name</h3>
                <div class="vm-name-display" id="vmNameDisplay">Enter hospital and department codes</div>
                <div id="vmNamingExplanation"></div>
            </div>

            <!-- Information Section -->
            <div class="info-section">
                <h4>📋 VM Naming Convention</h4>
                <ul>
                    <li><strong>With Names:</strong> [Hospital][Department][Initials]</li>
                    <li><strong>Without Names:</strong> [Hospital][Department]</li>
                    <li><strong>Example:</strong> CCH9SUPPJD or CCH9SUPP</li>
                </ul>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="submit-btn" id="submitBtn" disabled>
                🚀 Request VM Deployment
            </button>
        </form>
    </div>

    <script>
        // Form validation and VM name generation
        document.addEventListener('DOMContentLoaded', function() {
            const hospitalCode = document.getElementById('hospitalCode');
            const departmentCode = document.getElementById('departmentCode');
            const firstName = document.getElementById('firstName');
            const lastName = document.getElementById('lastName');
            const vmPreview = document.getElementById('vmPreview');
            const vmNameDisplay = document.getElementById('vmNameDisplay');
            const submitBtn = document.getElementById('submitBtn');

            function validateHospitalCode(value) {
                const validation = document.getElementById('hospitalCodeValidation');
                if (!value) {
                    validation.textContent = '';
                    validation.className = 'validation-message';
                    return false;
                } else if (value.length > 4) {
                    validation.textContent = 'Hospital code must be 4 characters or less';
                    validation.className = 'validation-message error';
                    return false;
                } else if (!/^[A-Z0-9]+$/.test(value)) {
                    validation.textContent = 'Hospital code must contain only letters and numbers';
                    validation.className = 'validation-message error';
                    return false;
                } else {
                    validation.textContent = 'Valid hospital code ✓';
                    validation.className = 'validation-message success';
                    return true;
                }
            }

            function validateDepartmentCode(value) {
                const validation = document.getElementById('departmentCodeValidation');
                if (!value) {
                    validation.textContent = '';
                    validation.className = 'validation-message';
                    return false;
                } else if (value.length > 4) {
                    validation.textContent = 'Department code must be 4 characters or less';
                    validation.className = 'validation-message error';
                    return false;
                } else if (!/^[A-Z0-9]+$/.test(value)) {
                    validation.textContent = 'Department code must contain only letters and numbers';
                    validation.className = 'validation-message error';
                    return false;
                } else {
                    validation.textContent = 'Valid department code ✓';
                    validation.className = 'validation-message success';
                    return true;
                }
            }

            function generateVMName() {
                const hospital = hospitalCode.value.toUpperCase();
                const department = departmentCode.value.toUpperCase();
                const first = firstName.value.trim();
                const last = lastName.value.trim();

                if (!hospital || !department) {
                    vmNameDisplay.textContent = 'Enter hospital and department codes';
                    vmPreview.classList.remove('show');
                    return '';
                }

                let vmName = hospital + department;

                if (first && last) {
                    vmName += first.charAt(0).toUpperCase() + last.charAt(0).toUpperCase();
                }

                // No suffix added - just Hospital + Department + Initials

                vmNameDisplay.textContent = vmName;
                vmPreview.classList.add('show');

                return vmName;
            }

            function validateForm() {
                const hospitalValue = hospitalCode.value.trim();
                const departmentValue = departmentCode.value.trim();

                // Only validate if fields have values (don't show errors on empty fields)
                const hospitalValid = hospitalValue ? validateHospitalCode(hospitalValue) : false;
                const departmentValid = departmentValue ? validateDepartmentCode(departmentValue) : false;
                const vmName = generateVMName();

                // Enable submit button only if both required fields are filled and valid
                const isValid = hospitalValue && departmentValue && hospitalValid && departmentValid && vmName;

                submitBtn.disabled = !isValid;
                submitBtn.style.opacity = isValid ? '1' : '0.6';
                submitBtn.style.cursor = isValid ? 'pointer' : 'not-allowed';
                submitBtn.style.backgroundColor = isValid ? '#e91e63' : '#ccc';

                return isValid;
            }

            // Event listeners
            hospitalCode.addEventListener('input', validateForm);
            departmentCode.addEventListener('input', validateForm);
            firstName.addEventListener('input', function() {
                generateVMName();
                validateForm(); // Update submit button when names change
            });
            lastName.addEventListener('input', function() {
                generateVMName();
                validateForm(); // Update submit button when names change
            });

            // Form submission
            document.getElementById('vmRequestForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (validateForm()) {
                    submitBtn.innerHTML = '⏳ Processing Request...';
                    submitBtn.disabled = true;
                    
                    // Here you would integrate with Microsoft Forms or Power Automate
                    setTimeout(() => {
                        alert('VM request submitted successfully! Deployment will begin automatically.');
                        this.reset();
                        vmPreview.classList.remove('show');
                        submitBtn.innerHTML = '🚀 Request VM Deployment';
                        submitBtn.disabled = true;
                    }, 2000);
                }
            });

            // Initial validation
            validateForm();
        });
    </script>
</body>
</html>
