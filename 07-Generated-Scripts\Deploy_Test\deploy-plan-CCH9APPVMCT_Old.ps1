# Auto-Generated Deployment Script
# VM: CCH9APPVMCT
# Action: plan
# Generated: 2025-07-08T13:10:47.621Z

param(
    [string]$TerraformDir = "D:\\Maspects\\MAHealth\\Terraform\\Scope_Demo-Environment"
)

Clear-Host
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Auto-Deploy: CCH9APPVMCT" -ForegroundColor Cyan
Write-Host "    Action: PLAN" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to Terraform directory
if (Test-Path $TerraformDir) {
    Set-Location $TerraformDir
    Write-Host "Changed to Terraform directory" -ForegroundColor Green
} else {
    Write-Host "Terraform directory not found: $TerraformDir" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check prerequisites
if (!(Test-Path "main.tf")) {
    Write-Host "main.tf not found in current directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
    Write-Host "Terraform not found in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Prerequisites checked" -ForegroundColor Green
Write-Host ""

try {
    # Create terraform.tfvars
    Write-Host "Creating terraform.tfvars..." -ForegroundColor Cyan

    $tfvarsContent = @"
vm_name = "CCH9APPVMCT"
vm_count = 1
vm_resourcegroup = "MAH9-SUP-CCH9-VMC"
vm_size = "Standard_D4s_v3"
vm_sku = "2022-datacenter"
vm_location = "East US 2"
vm_admin_username = "maoperator"
vm_admin_password = "M1t1g@t0r2025"
azure_devops_organization = "https://dev.azure.com/MAHealth"
azure_devops_teamproject = "MAH9-SCP-CCH9GI"
azure_devops_deploymentgroup = "MAH9-SCP-CCH9-DGR-DEV"
azure_devops_pat = "4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7"
azure_devops_agentfolder = "c:/Agent"
"@

    $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
    Write-Host "terraform.tfvars created" -ForegroundColor Green

    # Initialize Terraform
    Write-Host "Initializing Terraform..." -ForegroundColor Cyan
    terraform init
    if ($LASTEXITCODE -ne 0) { throw "Terraform init failed" }
    Write-Host "Terraform initialized" -ForegroundColor Green

    # Validate
    Write-Host "Validating configuration..." -ForegroundColor Cyan
    terraform validate
    if ($LASTEXITCODE -ne 0) { throw "Terraform validation failed" }
    Write-Host "Configuration validated" -ForegroundColor Green

    # Execute action
    Write-Host "Running terraform plan..." -ForegroundColor Cyan


    terraform plan -var-file="terraform.tfvars"
    if ($LASTEXITCODE -ne 0) { throw "Terraform plan failed" }

    Write-Host ""
    Write-Host "Terraform plan completed successfully!" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "Deployment failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Process completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "Press Enter to exit"