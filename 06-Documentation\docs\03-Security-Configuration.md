# VM Deployment Automation - Security Configuration

## 🔒 Security Overview

This document outlines the security configuration and best practices for the VM deployment automation solution. Security is implemented at multiple layers to ensure comprehensive protection.

## 🛡️ Security Architecture

### Defense in Depth Strategy

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  • Input Validation  • Parameter Sanitization              │
├─────────────────────────────────────────────────────────────┤
│                    Authentication Layer                     │
│  • Azure AD  • Service Principals  • Managed Identity      │
├─────────────────────────────────────────────────────────────┤
│                    Authorization Layer                      │
│  • RBAC  • Resource Scoping  • Least Privilege             │
├─────────────────────────────────────────────────────────────┤
│                    Data Protection Layer                    │
│  • Key Vault  • Encryption  • Secrets Management           │
├─────────────────────────────────────────────────────────────┤
│                    Network Security Layer                   │
│  • NSGs  • Private Endpoints  • Network Segmentation       │
├─────────────────────────────────────────────────────────────┤
│                    Infrastructure Layer                     │
│  • Azure Security Center  • Monitoring  • Compliance       │
└─────────────────────────────────────────────────────────────┘
```

## 🔐 Authentication and Authorization

### Service Principal Configuration

#### 1. Create Dedicated Service Principal

```bash
# Create service principal for Terraform automation
az ad sp create-for-rbac --name "terraform-vm-deployment" \
    --role "Contributor" \
    --scopes "/subscriptions/{subscription-id}/resourceGroups/{resource-group}" \
    --sdk-auth

# Output will include:
# - clientId (Application ID)
# - clientSecret (Password)
# - tenantId (Directory ID)
# - subscriptionId
```

#### 2. Assign Minimal Required Permissions

```bash
# Assign specific roles instead of Contributor
az role assignment create \
    --assignee {service-principal-id} \
    --role "Virtual Machine Contributor" \
    --scope "/subscriptions/{subscription-id}/resourceGroups/{resource-group}"

az role assignment create \
    --assignee {service-principal-id} \
    --role "Network Contributor" \
    --scope "/subscriptions/{subscription-id}/resourceGroups/{resource-group}"
```

### Azure DevOps Service Connection

#### Secure Service Connection Setup

1. **Navigate to Project Settings** → Service connections
2. **Create new Azure Resource Manager connection**
3. **Use Service Principal (manual)**
4. **Configure with minimal scope**:
   - Subscription ID: Your Azure subscription
   - Resource Group: Specific resource group only
   - Service Principal ID: From step above
   - Service Principal Key: Store in Azure Key Vault

## 🔑 Secrets Management

### Azure Key Vault Integration

#### 1. Create Key Vault

```bash
# Create Key Vault for secrets
az keyvault create \
    --name "terraform-vm-secrets" \
    --resource-group "security-rg" \
    --location "East US 2" \
    --enable-rbac-authorization

# Grant access to service principal
az role assignment create \
    --assignee {service-principal-id} \
    --role "Key Vault Secrets User" \
    --scope "/subscriptions/{subscription-id}/resourceGroups/security-rg/providers/Microsoft.KeyVault/vaults/terraform-vm-secrets"
```

#### 2. Store Secrets in Key Vault

```bash
# Store VM admin password
az keyvault secret set \
    --vault-name "terraform-vm-secrets" \
    --name "vm-admin-password" \
    --value "YourSecurePassword123!"

# Store Azure DevOps PAT token
az keyvault secret set \
    --vault-name "terraform-vm-secrets" \
    --name "azure-devops-pat" \
    --value "your-pat-token-here"

# Store service principal secret
az keyvault secret set \
    --vault-name "terraform-vm-secrets" \
    --name "terraform-sp-secret" \
    --value "service-principal-password"
```

#### 3. Update Terraform Configuration

```hcl
# Add Key Vault data source to main.tf
data "azurerm_key_vault" "secrets" {
  name                = "terraform-vm-secrets"
  resource_group_name = "security-rg"
}

data "azurerm_key_vault_secret" "vm_admin_password" {
  name         = "vm-admin-password"
  key_vault_id = data.azurerm_key_vault.secrets.id
}

data "azurerm_key_vault_secret" "azure_devops_pat" {
  name         = "azure-devops-pat"
  key_vault_id = data.azurerm_key_vault.secrets.id
}

# Use secrets in VM configuration
resource "azurerm_windows_virtual_machine" "vm" {
  # ... other configuration ...
  
  admin_password = data.azurerm_key_vault_secret.vm_admin_password.value
  
  # ... rest of configuration ...
}
```

### PowerShell Script Security Updates

```powershell
# Update Deploy-VM-Terraform.ps1 to use Key Vault
param(
    # ... other parameters ...
    
    [Parameter(Mandatory=$false)]
    [string]$KeyVaultName = "terraform-vm-secrets",
    
    [Parameter(Mandatory=$false)]
    [switch]$UseKeyVault = $false
)

if ($UseKeyVault) {
    Write-Log "Retrieving secrets from Key Vault: $KeyVaultName"
    
    # Get secrets from Key Vault
    $AdminPassword = (Get-AzKeyVaultSecret -VaultName $KeyVaultName -Name "vm-admin-password" -AsPlainText)
    $AzureDevOpsPat = (Get-AzKeyVaultSecret -VaultName $KeyVaultName -Name "azure-devops-pat" -AsPlainText)
    
    Write-Log "Secrets retrieved successfully from Key Vault"
}
```

## 🌐 Network Security

### Network Security Groups (NSGs)

#### 1. Create Restrictive NSG Rules

```bash
# Create NSG for VM subnet
az network nsg create \
    --resource-group "your-rg" \
    --name "vm-subnet-nsg"

# Allow RDP only from specific IP ranges (replace with your office IPs)
az network nsg rule create \
    --resource-group "your-rg" \
    --nsg-name "vm-subnet-nsg" \
    --name "Allow-RDP-Office" \
    --priority 1000 \
    --source-address-prefixes "***********/24" \
    --destination-port-ranges 3389 \
    --access Allow \
    --protocol Tcp

# Allow Azure DevOps agent communication
az network nsg rule create \
    --resource-group "your-rg" \
    --nsg-name "vm-subnet-nsg" \
    --name "Allow-DevOps-Agent" \
    --priority 1100 \
    --source-address-prefixes "VirtualNetwork" \
    --destination-port-ranges 443 \
    --access Allow \
    --protocol Tcp

# Deny all other inbound traffic
az network nsg rule create \
    --resource-group "your-rg" \
    --nsg-name "vm-subnet-nsg" \
    --name "Deny-All-Inbound" \
    --priority 4000 \
    --access Deny \
    --protocol "*"
```

#### 2. Update Terraform Configuration

```hcl
# Add NSG to Terraform configuration
resource "azurerm_network_security_group" "vm_nsg" {
  name                = "${var.vm_name}-nsg"
  location            = var.vm_location
  resource_group_name = var.vm_resourcegroup

  security_rule {
    name                       = "Allow-RDP-Office"
    priority                   = 1000
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "3389"
    source_address_prefix      = "***********/24"  # Replace with your office IP
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "Allow-HTTPS-Outbound"
    priority                   = 1000
    direction                  = "Outbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }
}

# Associate NSG with subnet
resource "azurerm_subnet_network_security_group_association" "vm_nsg_association" {
  subnet_id                 = azurerm_subnet.subnet.id
  network_security_group_id = azurerm_network_security_group.vm_nsg.id
}
```

### Private Endpoints (Optional)

For enhanced security, consider using private endpoints:

```hcl
# Private endpoint for Key Vault
resource "azurerm_private_endpoint" "keyvault_pe" {
  name                = "keyvault-private-endpoint"
  location            = var.vm_location
  resource_group_name = var.vm_resourcegroup
  subnet_id           = azurerm_subnet.private_endpoint_subnet.id

  private_service_connection {
    name                           = "keyvault-privateserviceconnection"
    private_connection_resource_id = data.azurerm_key_vault.secrets.id
    subresource_names              = ["vault"]
    is_manual_connection           = false
  }
}
```

## 🔍 Monitoring and Auditing

### Azure Monitor Configuration

#### 1. Enable VM Insights

```bash
# Enable monitoring for VMs
az monitor log-analytics workspace create \
    --resource-group "monitoring-rg" \
    --workspace-name "vm-monitoring-workspace"

# Get workspace ID
WORKSPACE_ID=$(az monitor log-analytics workspace show \
    --resource-group "monitoring-rg" \
    --workspace-name "vm-monitoring-workspace" \
    --query customerId -o tsv)
```

#### 2. Configure Security Monitoring

```hcl
# Add monitoring extension to VMs
resource "azurerm_virtual_machine_extension" "monitoring" {
  count                = var.vm_count
  name                 = "monitoring-${count.index + 1}"
  virtual_machine_id   = azurerm_windows_virtual_machine.vm[count.index].id
  publisher            = "Microsoft.EnterpriseCloud.Monitoring"
  type                 = "MicrosoftMonitoringAgent"
  type_handler_version = "1.0"

  settings = jsonencode({
    "workspaceId" = var.log_analytics_workspace_id
  })

  protected_settings = jsonencode({
    "workspaceKey" = var.log_analytics_workspace_key
  })
}
```

### Security Alerts

#### 1. Create Security Alerts

```bash
# Create alert for failed login attempts
az monitor metrics alert create \
    --name "VM-Failed-Logins" \
    --resource-group "monitoring-rg" \
    --scopes "/subscriptions/{subscription-id}/resourceGroups/{vm-rg}" \
    --condition "count 'Heartbeat' > 5" \
    --description "Alert when VM has failed login attempts"
```

#### 2. Configure Teams Notifications

Update Power Automate flow to include security alerts:

```json
{
  "type": "AdaptiveCard",
  "body": [
    {
      "type": "TextBlock",
      "text": "🚨 Security Alert",
      "weight": "Bolder",
      "color": "Attention"
    },
    {
      "type": "FactSet",
      "facts": [
        {
          "title": "Alert Type:",
          "value": "Failed Login Attempts"
        },
        {
          "title": "VM Name:",
          "value": "@{variables('vmName')}"
        },
        {
          "title": "Time:",
          "value": "@{utcNow()}"
        }
      ]
    }
  ]
}
```

## 📋 Compliance and Governance

### Azure Policy Implementation

#### 1. Create Custom Policy for VM Compliance

```json
{
  "mode": "All",
  "policyRule": {
    "if": {
      "allOf": [
        {
          "field": "type",
          "equals": "Microsoft.Compute/virtualMachines"
        },
        {
          "field": "Microsoft.Compute/virtualMachines/storageProfile.osDisk.encryptionSettings.enabled",
          "notEquals": "true"
        }
      ]
    },
    "then": {
      "effect": "deny"
    }
  },
  "parameters": {},
  "displayName": "Require VM Disk Encryption",
  "description": "This policy ensures that all VMs have disk encryption enabled"
}
```

#### 2. Assign Policy to Resource Group

```bash
# Assign policy to resource group
az policy assignment create \
    --name "vm-disk-encryption-required" \
    --policy "require-vm-disk-encryption" \
    --scope "/subscriptions/{subscription-id}/resourceGroups/{resource-group}"
```

### Backup and Recovery

#### 1. Configure Azure Backup

```hcl
# Recovery Services Vault
resource "azurerm_recovery_services_vault" "vm_backup" {
  name                = "${var.vm_name}-backup-vault"
  location            = var.vm_location
  resource_group_name = var.vm_resourcegroup
  sku                 = "Standard"
}

# Backup policy
resource "azurerm_backup_policy_vm" "vm_backup_policy" {
  name                = "${var.vm_name}-backup-policy"
  resource_group_name = var.vm_resourcegroup
  recovery_vault_name = azurerm_recovery_services_vault.vm_backup.name

  backup {
    frequency = "Daily"
    time      = "23:00"
  }

  retention_daily {
    count = 30
  }
}
```

## 🔒 Security Checklist

### Pre-Deployment Security Checklist

- [ ] Service principal created with minimal permissions
- [ ] Secrets stored in Azure Key Vault
- [ ] Network Security Groups configured
- [ ] Monitoring and alerting enabled
- [ ] Backup policies configured
- [ ] Azure Policy compliance verified
- [ ] Access controls reviewed and approved

### Post-Deployment Security Checklist

- [ ] VM security baseline applied
- [ ] Windows updates installed
- [ ] Antimalware enabled
- [ ] Firewall configured
- [ ] Audit logging enabled
- [ ] Security monitoring active
- [ ] Backup verification completed

### Ongoing Security Maintenance

- [ ] Regular security updates
- [ ] Access review (quarterly)
- [ ] Secret rotation (every 90 days)
- [ ] Security assessment (monthly)
- [ ] Compliance reporting
- [ ] Incident response testing

## 🚨 Security Incident Response

### Incident Response Plan

1. **Detection**: Automated alerts and monitoring
2. **Assessment**: Determine scope and impact
3. **Containment**: Isolate affected resources
4. **Eradication**: Remove threats and vulnerabilities
5. **Recovery**: Restore services and verify security
6. **Lessons Learned**: Document and improve processes

### Emergency Contacts

- **Security Team**: <EMAIL>
- **Azure Support**: Create support ticket
- **Management**: Escalation procedures documented

---

## 📞 Security Support

For security-related questions or incidents:

1. **Review this security guide** for configuration details
2. **Check Azure Security Center** for recommendations
3. **Contact security team** for policy questions
4. **Create support ticket** for Azure-specific issues

This security configuration provides comprehensive protection while maintaining operational efficiency. Regular reviews and updates ensure continued security posture improvement.
