/**
 * Comprehensive Validation and Error Handling for Microsoft Forms VM Deployment
 * Provides client-side and server-side validation with detailed error reporting
 */

class FormValidationHandler {
    constructor() {
        this.validationRules = {
            hospitalCode: {
                required: true,
                length: 4,
                pattern: /^[A-Z0-9]{4}$/,
                message: 'Hospital code must be exactly 4 alphanumeric characters'
            },
            departmentCode: {
                required: true,
                length: 4,
                pattern: /^[A-Z]{4}$/,
                message: 'Department code must be exactly 4 letters'
            },
            firstName: {
                required: true,
                minLength: 2,
                pattern: /^[A-Za-z]+$/,
                message: 'First name must be at least 2 letters'
            },
            lastName: {
                required: true,
                minLength: 2,
                pattern: /^[A-Za-z]+$/,
                message: 'Last name must be at least 2 letters'
            },
            vmType: {
                required: true,
                validValues: ['CT', 'DB', 'WB', 'AP', 'DC', 'FS', 'PS', 'TS'],
                message: 'VM type must be one of: CT, DB, WB, AP, DC, FS, PS, TS'
            },
            vmCount: {
                required: true,
                min: 1,
                max: 5,
                type: 'number',
                message: 'VM count must be between 1 and 5'
            }
        };
        
        this.errorMessages = {
            VALIDATION_FAILED: 'Form validation failed. Please correct the errors and try again.',
            NETWORK_ERROR: 'Network error occurred. Please check your connection and try again.',
            SERVER_ERROR: 'Server error occurred. Please contact IT support.',
            TIMEOUT_ERROR: 'Request timed out. Please try again.',
            DEPLOYMENT_FAILED: 'VM deployment failed. Please check the logs and try again.',
            UNAUTHORIZED: 'You are not authorized to perform this action.',
            RESOURCE_CONFLICT: 'A VM with this name already exists. Please use a different configuration.',
            QUOTA_EXCEEDED: 'VM quota exceeded. Please contact IT support to increase your quota.'
        };
    }

    /**
     * Validates a single field based on validation rules
     * @param {string} fieldName - Name of the field to validate
     * @param {any} value - Value to validate
     * @returns {object} Validation result
     */
    validateField(fieldName, value) {
        const rule = this.validationRules[fieldName];
        if (!rule) {
            return { valid: true };
        }

        const errors = [];

        // Required validation
        if (rule.required && (!value || value.toString().trim() === '')) {
            errors.push(`${fieldName} is required`);
            return { valid: false, errors, message: rule.message };
        }

        const stringValue = value ? value.toString().trim() : '';

        // Length validation
        if (rule.length && stringValue.length !== rule.length) {
            errors.push(`${fieldName} must be exactly ${rule.length} characters`);
        }

        // Min length validation
        if (rule.minLength && stringValue.length < rule.minLength) {
            errors.push(`${fieldName} must be at least ${rule.minLength} characters`);
        }

        // Pattern validation
        if (rule.pattern && !rule.pattern.test(stringValue)) {
            errors.push(rule.message);
        }

        // Valid values validation
        if (rule.validValues && !rule.validValues.includes(stringValue.toUpperCase())) {
            errors.push(`${fieldName} must be one of: ${rule.validValues.join(', ')}`);
        }

        // Number validation
        if (rule.type === 'number') {
            const numValue = parseInt(value);
            if (isNaN(numValue)) {
                errors.push(`${fieldName} must be a valid number`);
            } else {
                if (rule.min && numValue < rule.min) {
                    errors.push(`${fieldName} must be at least ${rule.min}`);
                }
                if (rule.max && numValue > rule.max) {
                    errors.push(`${fieldName} must be at most ${rule.max}`);
                }
            }
        }

        return {
            valid: errors.length === 0,
            errors: errors,
            message: errors.length > 0 ? errors[0] : null
        };
    }

    /**
     * Validates the entire form
     * @param {object} formData - Form data to validate
     * @returns {object} Validation result
     */
    validateForm(formData) {
        const results = {};
        const allErrors = [];

        // Validate each field
        Object.keys(this.validationRules).forEach(fieldName => {
            const result = this.validateField(fieldName, formData[fieldName]);
            results[fieldName] = result;
            if (!result.valid) {
                allErrors.push(...result.errors);
            }
        });

        // Additional cross-field validations
        const crossValidationErrors = this.performCrossValidation(formData);
        allErrors.push(...crossValidationErrors);

        return {
            valid: allErrors.length === 0,
            fieldResults: results,
            errors: allErrors,
            summary: allErrors.length === 0 ? 'All validations passed' : `${allErrors.length} validation error(s) found`
        };
    }

    /**
     * Performs cross-field validation
     * @param {object} formData - Form data
     * @returns {array} Array of validation errors
     */
    performCrossValidation(formData) {
        const errors = [];

        // Check for VM name length after generation
        if (formData.hospitalCode && formData.departmentCode && 
            formData.firstName && formData.lastName && formData.vmType) {
            
            const vmName = this.generateVMName(formData);
            if (vmName.length > 15) {
                errors.push('Generated VM name exceeds maximum length of 15 characters');
            }
        }

        // Check for reserved names or patterns
        const reservedPatterns = ['TEST', 'TEMP', 'DEMO', 'ADMIN'];
        const hospitalCode = formData.hospitalCode?.toUpperCase();
        const departmentCode = formData.departmentCode?.toUpperCase();
        
        if (hospitalCode && reservedPatterns.some(pattern => hospitalCode.includes(pattern))) {
            errors.push('Hospital code cannot contain reserved words');
        }
        
        if (departmentCode && reservedPatterns.some(pattern => departmentCode.includes(pattern))) {
            errors.push('Department code cannot contain reserved words');
        }

        return errors;
    }

    /**
     * Generates VM name for validation purposes
     * @param {object} formData - Form data
     * @returns {string} Generated VM name
     */
    generateVMName(formData) {
        const hospital = formData.hospitalCode?.toUpperCase() || '';
        const department = formData.departmentCode?.toUpperCase() || '';
        const firstInitial = formData.firstName?.charAt(0).toUpperCase() || '';
        const lastInitial = formData.lastName?.charAt(0).toUpperCase() || '';
        const vmType = formData.vmType?.toUpperCase() || 'CT';
        
        return `${hospital}${department}${firstInitial}${lastInitial}VM${vmType}`;
    }

    /**
     * Displays validation errors in the UI
     * @param {object} validationResult - Result from validateForm
     */
    displayValidationErrors(validationResult) {
        // Clear previous errors
        document.querySelectorAll('.validation-message').forEach(el => {
            el.classList.remove('show');
        });

        if (!validationResult.valid) {
            // Display field-specific errors
            Object.keys(validationResult.fieldResults).forEach(fieldName => {
                const fieldResult = validationResult.fieldResults[fieldName];
                if (!fieldResult.valid) {
                    const errorElement = document.getElementById(`${fieldName}Validation`);
                    if (errorElement) {
                        errorElement.textContent = fieldResult.message;
                        errorElement.classList.add('show');
                    }
                }
            });

            // Display general error summary
            this.showErrorNotification(validationResult.errors);
        }
    }

    /**
     * Shows error notification to user
     * @param {array} errors - Array of error messages
     */
    showErrorNotification(errors) {
        const errorHtml = `
            <div class="error-notification">
                <h4>❌ Validation Errors</h4>
                <ul>
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            </div>
        `;
        
        // Create or update error container
        let errorContainer = document.getElementById('errorContainer');
        if (!errorContainer) {
            errorContainer = document.createElement('div');
            errorContainer.id = 'errorContainer';
            errorContainer.className = 'error-container';
            document.querySelector('.form-container').appendChild(errorContainer);
        }
        
        errorContainer.innerHTML = errorHtml;
        errorContainer.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * Handles deployment errors from the server
     * @param {object} error - Error object from server
     */
    handleDeploymentError(error) {
        let errorMessage = this.errorMessages.SERVER_ERROR;
        let errorDetails = '';

        // Parse different types of errors
        if (error.status) {
            switch (error.status) {
                case 400:
                    errorMessage = this.errorMessages.VALIDATION_FAILED;
                    break;
                case 401:
                    errorMessage = this.errorMessages.UNAUTHORIZED;
                    break;
                case 409:
                    errorMessage = this.errorMessages.RESOURCE_CONFLICT;
                    break;
                case 429:
                    errorMessage = this.errorMessages.QUOTA_EXCEEDED;
                    break;
                case 500:
                    errorMessage = this.errorMessages.SERVER_ERROR;
                    break;
                case 504:
                    errorMessage = this.errorMessages.TIMEOUT_ERROR;
                    break;
            }
        }

        if (error.message) {
            errorDetails = error.message;
        }

        this.showDeploymentError(errorMessage, errorDetails, error);
    }

    /**
     * Shows deployment error notification
     * @param {string} message - Main error message
     * @param {string} details - Error details
     * @param {object} error - Full error object
     */
    showDeploymentError(message, details, error) {
        const errorHtml = `
            <div class="deployment-error-notification">
                <h4>🚨 Deployment Error</h4>
                <p><strong>${message}</strong></p>
                ${details ? `<p>Details: ${details}</p>` : ''}
                <div class="error-actions">
                    <button onclick="this.parentElement.parentElement.style.display='none'" class="btn btn-secondary">Dismiss</button>
                    <button onclick="window.location.reload()" class="btn btn-primary">Try Again</button>
                </div>
                ${error.deploymentId ? `<p><small>Deployment ID: ${error.deploymentId}</small></p>` : ''}
            </div>
        `;
        
        let errorContainer = document.getElementById('deploymentErrorContainer');
        if (!errorContainer) {
            errorContainer = document.createElement('div');
            errorContainer.id = 'deploymentErrorContainer';
            errorContainer.className = 'deployment-error-container';
            document.querySelector('.form-container').appendChild(errorContainer);
        }
        
        errorContainer.innerHTML = errorHtml;
        errorContainer.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * Clears all error messages
     */
    clearErrors() {
        document.querySelectorAll('.validation-message').forEach(el => {
            el.classList.remove('show');
        });
        
        const errorContainer = document.getElementById('errorContainer');
        if (errorContainer) {
            errorContainer.innerHTML = '';
        }
        
        const deploymentErrorContainer = document.getElementById('deploymentErrorContainer');
        if (deploymentErrorContainer) {
            deploymentErrorContainer.innerHTML = '';
        }
    }

    /**
     * Real-time validation for form fields
     * @param {HTMLElement} field - Form field element
     */
    setupRealTimeValidation(field) {
        field.addEventListener('input', (e) => {
            const fieldName = e.target.name;
            const value = e.target.value;
            
            const result = this.validateField(fieldName, value);
            const errorElement = document.getElementById(`${fieldName}Validation`);
            
            if (errorElement) {
                if (result.valid) {
                    errorElement.classList.remove('show');
                } else {
                    errorElement.textContent = result.message;
                    errorElement.classList.add('show');
                }
            }
            
            // Update character counter for length-based fields
            const counterElement = document.getElementById(`${fieldName}Counter`);
            if (counterElement && this.validationRules[fieldName]?.length) {
                const maxLength = this.validationRules[fieldName].length;
                counterElement.textContent = `${value.length}/${maxLength} characters`;
                counterElement.className = `char-counter ${result.valid ? 'success' : 'error'}`;
            }
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FormValidationHandler;
}

// Browser environment setup
if (typeof window !== 'undefined') {
    window.FormValidationHandler = FormValidationHandler;
    
    // Initialize validation when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        const validator = new FormValidationHandler();
        
        // Setup real-time validation for all form fields
        document.querySelectorAll('input, select').forEach(field => {
            if (field.name && validator.validationRules[field.name]) {
                validator.setupRealTimeValidation(field);
            }
        });
        
        // Make validator globally available
        window.formValidator = validator;
    });
}
