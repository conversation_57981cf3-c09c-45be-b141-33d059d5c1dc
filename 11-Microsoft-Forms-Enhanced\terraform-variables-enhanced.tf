# Enhanced Terraform Variables for Microsoft Forms Integration
# Supports automated VM deployment from form submissions

# Core VM Configuration
variable "vm_name" {
  description = "Name of the VM to be created (generated from form)"
  type        = string
  default     = "DEFAULTVMCT"
  
  validation {
    condition     = length(var.vm_name) <= 15 && length(var.vm_name) >= 6
    error_message = "VM name must be between 6 and 15 characters."
  }
}

variable "vm_count" {
  description = "Number of VMs to create"
  type        = number
  default     = 1
  
  validation {
    condition     = var.vm_count >= 1 && var.vm_count <= 5
    error_message = "VM count must be between 1 and 5."
  }
}

variable "vm_resourcegroup" {
  description = "Azure Resource Group for VM deployment"
  type        = string
  default     = "MAH9-SUP-CCH9-VMC"
}

variable "vm_size" {
  description = "Azure VM size/SKU"
  type        = string
  default     = "Standard_D4s_v3"
  
  validation {
    condition = contains([
      "Standard_B2s", "Standard_B2ms", "Standard_B4ms",
      "Standard_D2s_v3", "Standard_D4s_v3", "Standard_D8s_v3",
      "Standard_E2s_v3", "Standard_E4s_v3", "Standard_E8s_v3"
    ], var.vm_size)
    error_message = "VM size must be a valid Azure VM SKU."
  }
}

variable "vm_location" {
  description = "Azure region for VM deployment"
  type        = string
  default     = "East US 2"
}

variable "vm_sku" {
  description = "Windows Server SKU"
  type        = string
  default     = "2022-datacenter"
  
  validation {
    condition = contains([
      "2019-datacenter", "2022-datacenter", 
      "2019-datacenter-azure-edition", "2022-datacenter-azure-edition"
    ], var.vm_sku)
    error_message = "VM SKU must be a valid Windows Server version."
  }
}

# VM Authentication
variable "vm_admin_username" {
  description = "Administrator username for the VM"
  type        = string
  default     = "maoperator"
  sensitive   = true
}

variable "vm_admin_password" {
  description = "Administrator password for the VM"
  type        = string
  default     = "M1t1g@t0r2025"
  sensitive   = true
  
  validation {
    condition     = length(var.vm_admin_password) >= 12
    error_message = "Password must be at least 12 characters long."
  }
}

# Azure DevOps Integration
variable "azure_devops_organization" {
  description = "Azure DevOps organization URL"
  type        = string
  default     = "https://dev.azure.com/MAHealth"
}

variable "azure_devops_teamproject" {
  description = "Azure DevOps team project name"
  type        = string
  default     = "MAH9-SCP-CCH9GI"
}

variable "azure_devops_deploymentgroup" {
  description = "Azure DevOps deployment group name"
  type        = string
  default     = "MAH9-SCP-CCH9-DGR-DEV"
}

variable "azure_devops_pat" {
  description = "Azure DevOps Personal Access Token"
  type        = string
  default     = "your-pat-token-here"
  sensitive   = true
}

variable "azure_devops_agentfolder" {
  description = "Folder path for Azure DevOps agent installation"
  type        = string
  default     = "c:/Agent"
}

# Form Metadata Variables (for tracking and tagging)
variable "deployment_id" {
  description = "Unique deployment identifier from form submission"
  type        = string
  default     = ""
}

variable "hospital_code" {
  description = "Hospital code from form (4 characters max)"
  type        = string
  default     = ""
  
  validation {
    condition     = length(var.hospital_code) <= 4
    error_message = "Hospital code must be 4 characters or less."
  }
}

variable "department_code" {
  description = "Department code from form (4 characters max)"
  type        = string
  default     = ""
  
  validation {
    condition     = length(var.department_code) <= 4
    error_message = "Department code must be 4 characters or less."
  }
}

variable "requested_by" {
  description = "Name of person who requested the VM"
  type        = string
  default     = "System Request"
}

variable "request_timestamp" {
  description = "Timestamp when the VM was requested"
  type        = string
  default     = ""
}

# Network Configuration
variable "vm_subnet_address_prefix" {
  description = "Address prefix for VM subnet"
  type        = string
  default     = "********/24"
}

variable "vm_vnet_address_space" {
  description = "Address space for virtual network"
  type        = list(string)
  default     = ["10.0.0.0/16"]
}

# Tagging
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Environment   = "Production"
    Project       = "Hospital-VM-Automation"
    ManagedBy     = "Terraform"
    Source        = "Microsoft-Forms"
  }
}

# Computed local values for enhanced functionality
locals {
  # Generate resource tags with form metadata
  resource_tags = merge(var.common_tags, {
    VMName        = var.vm_name
    HospitalCode  = var.hospital_code
    DepartmentCode = var.department_code
    RequestedBy   = var.requested_by
    DeploymentId  = var.deployment_id
    CreatedDate   = formatdate("YYYY-MM-DD", timestamp())
  })
  
  # Generate VM naming components
  vm_base_name = var.vm_name
  vm_names = var.vm_count > 1 ? [
    for i in range(var.vm_count) : "${var.vm_name}${format("%02d", i + 1)}"
  ] : [var.vm_name]
  
  # Network security group rules for hospital environment
  nsg_rules = [
    {
      name                       = "RDP"
      priority                   = 1001
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "Tcp"
      source_port_range          = "*"
      destination_port_range     = "3389"
      source_address_prefix      = "10.0.0.0/8"
      destination_address_prefix = "*"
    },
    {
      name                       = "HTTP"
      priority                   = 1002
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "Tcp"
      source_port_range          = "*"
      destination_port_range     = "80"
      source_address_prefix      = "10.0.0.0/8"
      destination_address_prefix = "*"
    },
    {
      name                       = "HTTPS"
      priority                   = 1003
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "Tcp"
      source_port_range          = "*"
      destination_port_range     = "443"
      source_address_prefix      = "10.0.0.0/8"
      destination_address_prefix = "*"
    }
  ]
}

# Output values for pipeline and notifications
output "vm_names" {
  description = "Names of created VMs"
  value       = local.vm_names
}

output "deployment_metadata" {
  description = "Deployment metadata from form"
  value = {
    deployment_id   = var.deployment_id
    hospital_code   = var.hospital_code
    department_code = var.department_code
    requested_by    = var.requested_by
    vm_name         = var.vm_name
    vm_count        = var.vm_count
  }
}

output "resource_tags" {
  description = "Applied resource tags"
  value       = local.resource_tags
}
