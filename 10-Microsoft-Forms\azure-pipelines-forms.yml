# Azure DevOps Pipeline for Microsoft Forms VM Deployment
# Triggered by Power Automate when a Microsoft Form is submitted
# Automatically deploys VMs using Terraform based on form data

trigger: none # This pipeline is triggered by Power Automate, not by code changes

pr: none

parameters:
- name: hospitalCode
  displayName: 'Hospital Code (4 characters)'
  type: string
  
- name: departmentCode
  displayName: 'Department Code (4 characters)'
  type: string
  
- name: firstName
  displayName: 'First Name'
  type: string
  
- name: lastName
  displayName: 'Last Name'
  type: string
  
- name: vmType
  displayName: 'VM Type'
  type: string
  default: 'CT'
  values:
  - CT
  - DB
  - WB
  - AP
  - DC
  - FS
  - PS
  - TS
  
- name: vmCount
  displayName: 'Number of VMs'
  type: number
  default: 1
  
- name: deploymentId
  displayName: 'Deployment ID'
  type: string
  default: ''
  
- name: action
  displayName: 'Terraform Action'
  type: string
  default: 'apply'
  values:
  - plan
  - apply
  - destroy

variables:
- group: 'terraform-secrets' # Variable group containing sensitive values
- name: vmName
  value: '${{ parameters.hospitalCode }}${{ parameters.departmentCode }}${{ upper(substring(parameters.firstName, 0, 1)) }}${{ upper(substring(parameters.lastName, 0, 1)) }}VM${{ parameters.vmType }}'
- name: requestedBy
  value: '${{ parameters.firstName }} ${{ parameters.lastName }}'
- name: deploymentIdGenerated
  value: $[coalesce('${{ parameters.deploymentId }}', format('DEP-{0:yyyyMMddHHmmss}-{1}', pipeline.startTime, substring(variables['Build.BuildId'], 0, 8)))]

pool:
  vmImage: 'windows-latest'

stages:
- stage: Validate
  displayName: 'Validate Form Input'
  jobs:
  - job: ValidateInput
    displayName: 'Validate Form Data'
    steps:
    - task: PowerShell@2
      displayName: 'Validate Input Parameters'
      inputs:
        targetType: 'inline'
        script: |
          # Validate hospital code
          if ('${{ parameters.hospitalCode }}'.Length -ne 4) {
            Write-Error "Hospital code must be exactly 4 characters"
            exit 1
          }
          
          # Validate department code
          if ('${{ parameters.departmentCode }}'.Length -ne 4) {
            Write-Error "Department code must be exactly 4 characters"
            exit 1
          }
          
          # Validate names
          if ('${{ parameters.firstName }}'.Length -lt 2 -or '${{ parameters.lastName }}'.Length -lt 2) {
            Write-Error "First and last names must be at least 2 characters"
            exit 1
          }
          
          # Validate VM count
          if (${{ parameters.vmCount }} -lt 1 -or ${{ parameters.vmCount }} -gt 5) {
            Write-Error "VM count must be between 1 and 5"
            exit 1
          }
          
          Write-Host "✅ All input validation passed"
          Write-Host "Generated VM Name: $(vmName)"
          Write-Host "Deployment ID: $(deploymentIdGenerated)"
          Write-Host "Requested By: $(requestedBy)"

- stage: Plan
  displayName: 'Terraform Plan'
  dependsOn: Validate
  condition: succeeded()
  jobs:
  - job: TerraformPlan
    displayName: 'Generate Terraform Plan'
    steps:
    - checkout: self
    
    - task: PowerShell@2
      displayName: 'Generate Terraform Variables'
      inputs:
        targetType: 'inline'
        script: |
          $varsContent = @"
          # Terraform variables generated from Microsoft Forms submission
          # Generated on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
          # Deployment ID: $(deploymentIdGenerated)
          
          # Core VM Configuration
          vm_name = "$(vmName)"
          vm_count = ${{ parameters.vmCount }}
          vm_resourcegroup = "$(resourceGroup)"
          vm_size = "$(vmSize)"
          vm_location = "$(location)"
          
          # Form-specific metadata
          hospital_code = "${{ parameters.hospitalCode }}"
          department_code = "${{ parameters.departmentCode }}"
          user_first_name = "${{ parameters.firstName }}"
          user_last_name = "${{ parameters.lastName }}"
          vm_type_code = "${{ parameters.vmType }}"
          
          # Deployment tracking
          deployment_id = "$(deploymentIdGenerated)"
          requested_by = "$(requestedBy)"
          request_date = "$(Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")"
          
          # VM Configuration
          vm_offer = "WindowsServer"
          vm_publisher = "MicrosoftWindowsServer"
          vm_sku = "2022-datacenter-azure-edition"
          vm_admin_username = "$(adminUsername)"
          vm_admin_password = "$(adminPassword)"
          
          # Azure DevOps Configuration
          azure_devops_organization = "$(azureDevOpsOrg)"
          azure_devops_teamproject = "$(teamProject)"
          azure_devops_deploymentgroup = "$(deploymentGroup)"
          azure_devops_pat = "$(azureDevOpsPat)"
          azure_devops_agentfolder = "$(agentFolder)"
          "@
          
          $varsContent | Out-File -FilePath "terraform-forms.tfvars" -Encoding UTF8
          Write-Host "✅ Terraform variables file created"
    
    - task: TerraformInstaller@0
      displayName: 'Install Terraform'
      inputs:
        terraformVersion: 'latest'
    
    - task: TerraformTaskV3@3
      displayName: 'Terraform Init'
      inputs:
        provider: 'azurerm'
        command: 'init'
        workingDirectory: '01-Terraform-Core'
        backendServiceArm: '$(azureServiceConnection)'
        backendAzureRmResourceGroupName: '$(terraformStateResourceGroup)'
        backendAzureRmStorageAccountName: '$(terraformStateStorageAccount)'
        backendAzureRmContainerName: 'tfstate'
        backendAzureRmKey: 'forms-$(deploymentIdGenerated).tfstate'
    
    - task: TerraformTaskV3@3
      displayName: 'Terraform Plan'
      inputs:
        provider: 'azurerm'
        command: 'plan'
        workingDirectory: '01-Terraform-Core'
        commandOptions: '-var-file="../terraform-forms.tfvars" -out="tfplan-$(deploymentIdGenerated)"'
        environmentServiceNameAzureRM: '$(azureServiceConnection)'
    
    - task: PublishPipelineArtifact@1
      displayName: 'Publish Terraform Plan'
      inputs:
        targetPath: '01-Terraform-Core/tfplan-$(deploymentIdGenerated)'
        artifact: 'terraform-plan'
        publishLocation: 'pipeline'

- stage: Deploy
  displayName: 'Deploy Infrastructure'
  dependsOn: Plan
  condition: and(succeeded(), eq('${{ parameters.action }}', 'apply'))
  jobs:
  - deployment: DeployVMs
    displayName: 'Deploy VMs with Terraform'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: self
          
          - task: DownloadPipelineArtifact@2
            displayName: 'Download Terraform Plan'
            inputs:
              buildType: 'current'
              artifactName: 'terraform-plan'
              targetPath: '01-Terraform-Core'
          
          - task: PowerShell@2
            displayName: 'Recreate Terraform Variables'
            inputs:
              targetType: 'inline'
              script: |
                # Recreate the variables file (since it's not in the artifact)
                $varsContent = @"
                vm_name = "$(vmName)"
                vm_count = ${{ parameters.vmCount }}
                vm_resourcegroup = "$(resourceGroup)"
                vm_size = "$(vmSize)"
                vm_location = "$(location)"
                hospital_code = "${{ parameters.hospitalCode }}"
                department_code = "${{ parameters.departmentCode }}"
                user_first_name = "${{ parameters.firstName }}"
                user_last_name = "${{ parameters.lastName }}"
                vm_type_code = "${{ parameters.vmType }}"
                deployment_id = "$(deploymentIdGenerated)"
                requested_by = "$(requestedBy)"
                request_date = "$(Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")"
                vm_offer = "WindowsServer"
                vm_publisher = "MicrosoftWindowsServer"
                vm_sku = "2022-datacenter-azure-edition"
                vm_admin_username = "$(adminUsername)"
                vm_admin_password = "$(adminPassword)"
                azure_devops_organization = "$(azureDevOpsOrg)"
                azure_devops_teamproject = "$(teamProject)"
                azure_devops_deploymentgroup = "$(deploymentGroup)"
                azure_devops_pat = "$(azureDevOpsPat)"
                azure_devops_agentfolder = "$(agentFolder)"
                "@
                
                $varsContent | Out-File -FilePath "terraform-forms.tfvars" -Encoding UTF8
          
          - task: TerraformInstaller@0
            displayName: 'Install Terraform'
            inputs:
              terraformVersion: 'latest'
          
          - task: TerraformTaskV3@3
            displayName: 'Terraform Init'
            inputs:
              provider: 'azurerm'
              command: 'init'
              workingDirectory: '01-Terraform-Core'
              backendServiceArm: '$(azureServiceConnection)'
              backendAzureRmResourceGroupName: '$(terraformStateResourceGroup)'
              backendAzureRmStorageAccountName: '$(terraformStateStorageAccount)'
              backendAzureRmContainerName: 'tfstate'
              backendAzureRmKey: 'forms-$(deploymentIdGenerated).tfstate'
          
          - task: TerraformTaskV3@3
            displayName: 'Terraform Apply'
            inputs:
              provider: 'azurerm'
              command: 'apply'
              workingDirectory: '01-Terraform-Core'
              commandOptions: 'tfplan-$(deploymentIdGenerated)'
              environmentServiceNameAzureRM: '$(azureServiceConnection)'
          
          - task: PowerShell@2
            displayName: 'Generate Deployment Summary'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "🎉 VM Deployment Completed Successfully!"
                Write-Host "=================================="
                Write-Host "Deployment ID: $(deploymentIdGenerated)"
                Write-Host "VM Name: $(vmName)"
                Write-Host "VM Count: ${{ parameters.vmCount }}"
                Write-Host "Hospital: ${{ parameters.hospitalCode }}"
                Write-Host "Department: ${{ parameters.departmentCode }}"
                Write-Host "Requested By: $(requestedBy)"
                Write-Host "VM Type: ${{ parameters.vmType }}"
                Write-Host "Completed At: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
                Write-Host "=================================="

- stage: Destroy
  displayName: 'Destroy Infrastructure'
  dependsOn: Validate
  condition: and(succeeded(), eq('${{ parameters.action }}', 'destroy'))
  jobs:
  - job: DestroyInfrastructure
    displayName: 'Destroy VMs with Terraform'
    steps:
    - checkout: self
    
    - task: PowerShell@2
      displayName: 'Generate Terraform Variables for Destroy'
      inputs:
        targetType: 'inline'
        script: |
          # Create minimal variables file for destroy operation
          $varsContent = @"
          vm_name = "$(vmName)"
          vm_count = ${{ parameters.vmCount }}
          vm_resourcegroup = "$(resourceGroup)"
          hospital_code = "${{ parameters.hospitalCode }}"
          department_code = "${{ parameters.departmentCode }}"
          user_first_name = "${{ parameters.firstName }}"
          user_last_name = "${{ parameters.lastName }}"
          vm_type_code = "${{ parameters.vmType }}"
          azure_devops_pat = "$(azureDevOpsPat)"
          "@
          
          $varsContent | Out-File -FilePath "terraform-forms.tfvars" -Encoding UTF8
    
    - task: TerraformInstaller@0
      displayName: 'Install Terraform'
      inputs:
        terraformVersion: 'latest'
    
    - task: TerraformTaskV3@3
      displayName: 'Terraform Init'
      inputs:
        provider: 'azurerm'
        command: 'init'
        workingDirectory: '01-Terraform-Core'
        backendServiceArm: '$(azureServiceConnection)'
        backendAzureRmResourceGroupName: '$(terraformStateResourceGroup)'
        backendAzureRmStorageAccountName: '$(terraformStateStorageAccount)'
        backendAzureRmContainerName: 'tfstate'
        backendAzureRmKey: 'forms-$(deploymentIdGenerated).tfstate'
    
    - task: TerraformTaskV3@3
      displayName: 'Terraform Destroy'
      inputs:
        provider: 'azurerm'
        command: 'destroy'
        workingDirectory: '01-Terraform-Core'
        commandOptions: '-var-file="../terraform-forms.tfvars" -auto-approve'
        environmentServiceNameAzureRM: '$(azureServiceConnection)'
