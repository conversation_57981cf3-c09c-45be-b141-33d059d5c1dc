#!/usr/bin/env python3
"""
Simple HTTP server for Terraform VM deployment form
No additional dependencies required - uses built-in Python modules
"""

import http.server
import socketserver
import json
import subprocess
import threading
import os
import sys
from urllib.parse import parse_qs
import time

PORT = 8080
deployments = {}

class TerraformHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            # Serve the main form
            try:
                with open('vm-deployment-form-auto.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                self.send_response(200)
                self.send_header('Content-type', 'text/html')
                self.end_headers()
                self.wfile.write(content.encode('utf-8'))
            except FileNotFoundError:
                self.send_error(404, "Form file not found")
        
        elif self.path.startswith('/api/health'):
            # Health check
            response = {
                'status': 'healthy',
                'timestamp': time.time(),
                'activeDeployments': len(deployments)
            }
            self.send_json_response(response)
        
        elif self.path.startswith('/api/status/'):
            # Get deployment status
            deployment_id = self.path.split('/')[-1]
            if deployment_id in deployments:
                self.send_json_response(deployments[deployment_id])
            else:
                self.send_error(404, "Deployment not found")
        
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path == '/api/deploy':
            # Handle deployment request
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode('utf-8'))
                deployment_id = str(int(time.time() * 1000))  # Simple ID generation
                
                # Initialize deployment status
                deployments[deployment_id] = {
                    'status': 'starting',
                    'message': 'Deployment initiated',
                    'startTime': time.time(),
                    'action': data.get('action', 'apply'),
                    'vmName': data.get('vmName', 'DefaultVM'),
                    'logs': []
                }
                
                # Start deployment in background thread
                thread = threading.Thread(
                    target=self.execute_deployment,
                    args=(deployment_id, data)
                )
                thread.daemon = True
                thread.start()
                
                # Send immediate response
                response = {
                    'success': True,
                    'deploymentId': deployment_id,
                    'message': 'Deployment started',
                    'status': 'starting'
                }
                self.send_json_response(response)
                
            except Exception as e:
                self.send_error(500, f"Error processing request: {str(e)}")
        else:
            self.send_error(404, "Not found")
    
    def send_json_response(self, data):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        response = json.dumps(data)
        self.wfile.write(response.encode('utf-8'))
    
    def execute_deployment(self, deployment_id, params):
        """Execute Terraform deployment in background"""
        try:
            # Update status
            deployments[deployment_id]['status'] = 'running'
            deployments[deployment_id]['message'] = 'Creating terraform.tfvars...'
            
            # Create terraform.tfvars
            tfvars_content = f'''vm_name = "{params.get('vmName', 'DefaultVM')}"
vm_count = {params.get('vmCount', 1)}
vm_resourcegroup = "{params.get('resourceGroup', 'MAH9-SUP-CCH9-VMC')}"
vm_size = "{params.get('vmSize', 'Standard_D4s_v3')}"
vm_sku = "{params.get('vmSku', '2022-datacenter')}"
vm_location = "{params.get('location', 'East US 2')}"
vm_admin_username = "{params.get('adminUsername', 'maoperator')}"
vm_admin_password = "{params.get('adminPassword', 'M1t1g@t0r2025')}"
azure_devops_organization = "{params.get('azureDevOpsOrg', 'https://dev.azure.com/MAHealth')}"
azure_devops_teamproject = "{params.get('teamProject', 'MAH9-SCP-CCH9GI')}"
azure_devops_deploymentgroup = "{params.get('deploymentGroup', 'MAH9-SCP-CCH9-DGR-DEV')}"
azure_devops_pat = "{params.get('azureDevOpsPat', 'your-pat-token')}"
azure_devops_agentfolder = "{params.get('agentFolder', 'c:/Agent')}"
'''
            
            with open('terraform.tfvars', 'w') as f:
                f.write(tfvars_content)
            
            self.add_log(deployment_id, 'terraform.tfvars created successfully')
            
            # Execute Terraform commands
            action = params.get('action', 'apply')
            
            # Initialize
            deployments[deployment_id]['message'] = 'Initializing Terraform...'
            self.run_terraform_command(deployment_id, ['terraform', 'init'])
            
            # Validate
            deployments[deployment_id]['message'] = 'Validating configuration...'
            self.run_terraform_command(deployment_id, ['terraform', 'validate'])
            
            # Execute action
            if action == 'plan':
                deployments[deployment_id]['message'] = 'Running Terraform plan...'
                self.run_terraform_command(deployment_id, ['terraform', 'plan', '-var-file=terraform.tfvars'])
            elif action == 'apply':
                deployments[deployment_id]['message'] = 'Applying Terraform configuration...'
                self.run_terraform_command(deployment_id, ['terraform', 'apply', '-var-file=terraform.tfvars', '-auto-approve'])
            elif action == 'destroy':
                deployments[deployment_id]['message'] = 'Destroying infrastructure...'
                self.run_terraform_command(deployment_id, ['terraform', 'destroy', '-var-file=terraform.tfvars', '-auto-approve'])
            
            # Mark as completed
            deployments[deployment_id]['status'] = 'completed'
            deployments[deployment_id]['message'] = f'Terraform {action} completed successfully!'
            deployments[deployment_id]['endTime'] = time.time()
            
        except Exception as e:
            deployments[deployment_id]['status'] = 'failed'
            deployments[deployment_id]['message'] = f'Deployment failed: {str(e)}'
            deployments[deployment_id]['endTime'] = time.time()
            self.add_log(deployment_id, f'ERROR: {str(e)}')
    
    def run_terraform_command(self, deployment_id, command):
        """Run a Terraform command and capture output"""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                cwd=os.getcwd()
            )
            
            if result.stdout:
                self.add_log(deployment_id, result.stdout)
            if result.stderr:
                self.add_log(deployment_id, f'STDERR: {result.stderr}')
            
            if result.returncode != 0:
                raise Exception(f'Command failed: {" ".join(command)}')
                
        except Exception as e:
            raise Exception(f'Error running {" ".join(command)}: {str(e)}')
    
    def add_log(self, deployment_id, message):
        """Add log entry to deployment"""
        if deployment_id in deployments:
            deployments[deployment_id]['logs'].append({
                'timestamp': time.time(),
                'message': message.strip()
            })

def main():
    # Check if we're in the right directory
    if not os.path.exists('main.tf'):
        print("ERROR: main.tf not found!")
        print("Please run this script from your Terraform directory.")
        print(f"Current directory: {os.getcwd()}")
        sys.exit(1)
    
    # Check if form exists
    if not os.path.exists('vm-deployment-form-auto.html'):
        print("ERROR: vm-deployment-form-auto.html not found!")
        print("Please ensure the form file is in the current directory.")
        sys.exit(1)
    
    print("=" * 50)
    print("🚀 Terraform Web Server (Python)")
    print("=" * 50)
    print(f"📁 Working directory: {os.getcwd()}")
    print(f"🌐 Starting server on: http://localhost:{PORT}")
    print("⚡ Ready to deploy VMs automatically!")
    print("")
    print("Open your browser and go to: http://localhost:8080")
    print("Press Ctrl+C to stop the server")
    print("")
    
    try:
        with socketserver.TCPServer(("", PORT), TerraformHandler) as httpd:
            print(f"✅ Server started successfully on port {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down server...")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
