# Microsoft Forms to Azure DevOps VM Deployment Automation

## 🎯 Overview

This solution provides a **fully automated** VM deployment workflow where:
1. **Customer/Sales/CS Team** fills out a Microsoft Form
2. **Form submission** automatically triggers VM deployment via Azure DevOps
3. **Zero manual intervention** required after form submission

## 📋 Requirements Met

✅ **Hospital name/code**: 4 characters maximum  
✅ **Department**: 4 characters maximum (e.g., Supply = SUPP)  
✅ **User names**: Optional first/last name  
✅ **Automated VM naming**: `[Hospital][Department][Initials]VMCT` or `[Hospital][Department]VMCT`  
✅ **DevOps integration**: Automatic Terraform execution  
✅ **Organized structure**: Separate folders for different deployment options  

## 🏗️ Architecture

```
Microsoft Form → Power Automate → Azure DevOps Pipeline → Terraform → Azure VM
```

## 📁 File Structure

```
11-Microsoft-Forms-Enhanced/
├── vm-request-form-simplified.html          # Simplified form for users
├── power-automate-simplified-flow.json     # Power Automate flow configuration
├── azure-pipelines-forms-automation.yml    # Azure DevOps pipeline
├── SETUP-GUIDE-COMPLETE.md                 # This setup guide
├── terraform-variables-enhanced.tf         # Enhanced Terraform variables
└── deployment-scripts/                     # PowerShell deployment scripts
```

## 🚀 Step-by-Step Setup

### Step 1: Create Microsoft Form

1. **Go to Microsoft Forms** (forms.office.com)
2. **Create a new form** with these questions:

   **Question 1: Hospital Name/Code** (Required)
   - Type: Text
   - Settings: Required, Max 4 characters
   - Placeholder: "e.g., CCH9, MGH1"

   **Question 2: Department** (Required)
   - Type: Text  
   - Settings: Required, Max 4 characters
   - Placeholder: "e.g., SUPP, CARD, EMER"

   **Question 3: First Name** (Optional)
   - Type: Text
   - Settings: Optional
   - Placeholder: "John"

   **Question 4: Last Name** (Optional)
   - Type: Text
   - Settings: Optional
   - Placeholder: "Doe"

3. **Copy the Form ID** from the URL (you'll need this for Power Automate)

### Step 2: Setup Azure DevOps Pipeline

1. **Create Variable Group** named `terraform-secrets`:
   ```yaml
   azureServiceConnection: "your-azure-service-connection"
   resourceGroup: "MAH9-SUP-CCH9-VMC"
   vmSize: "Standard_D4s_v3"
   location: "East US 2"
   adminUsername: "maoperator"
   adminPassword: "M1t1g@t0r2025"
   azureDevOpsOrg: "https://dev.azure.com/MAHealth"
   teamProject: "MAH9-SCP-CCH9GI"
   deploymentGroup: "MAH9-SCP-CCH9-DGR-DEV"
   azureDevOpsPat: "your-devops-pat-token"
   agentFolder: "c:/Agent"
   terraformStateResourceGroup: "terraform-state-rg"
   terraformStateStorageAccount: "terraformstatesa"
   ```

2. **Create Pipeline**:
   - Use `azure-pipelines-forms-automation.yml`
   - Set trigger to "none" (API/manual only)
   - Configure service connections

3. **Get Pipeline ID** from the URL for Power Automate configuration

### Step 3: Configure Power Automate Flow

1. **Import Flow**:
   - Use `power-automate-simplified-flow.json`
   - Update the Form ID in the trigger
   - Update the Pipeline ID in the HTTP action

2. **Configure Connections**:
   - Microsoft Forms connection
   - Microsoft Teams connection (for notifications)

3. **Update Parameters**:
   ```json
   {
     "subscriptionId": "your-subscription-id",
     "devOpsOrganization": "https://dev.azure.com/MAHealth",
     "devOpsProject": "MAH9-SCP-CCH9GI",
     "pipelineId": "your-pipeline-id",
     "teamsChannelId": "your-teams-channel-id"
   }
   ```

4. **Set Azure DevOps PAT**:
   - Replace `YOUR_AZURE_DEVOPS_PAT` with your actual PAT token
   - Ensure PAT has permissions for: Build (read & execute), Release (read, write & execute)

### Step 4: Test the Complete Workflow

1. **Submit Test Form**:
   ```
   Hospital Code: CCH9
   Department: SUPP
   First Name: John
   Last Name: Doe
   ```

2. **Expected Results**:
   - VM Name Generated: `CCH9SUPPJDVMCT`
   - Power Automate flow triggers
   - Teams notification sent
   - Azure DevOps pipeline starts
   - Terraform deploys VM
   - Completion notification sent

## 🔧 VM Naming Convention

### With User Names
- **Format**: `[Hospital][Department][FirstInitial][LastInitial]VMCT`
- **Example**: `CCH9SUPPJDVMCT` (Cleveland Clinic Hospital 9, Supply, John Doe, Compute Type)

### Without User Names  
- **Format**: `[Hospital][Department]VMCT`
- **Example**: `CCH9SUPPVMCT` (Cleveland Clinic Hospital 9, Supply, Compute Type)

## 📊 Monitoring and Notifications

### Teams Notifications Include:
- ✅ **Request Received**: Form submission confirmation
- 🚀 **Deployment Started**: Pipeline execution begins
- ✅ **Deployment Complete**: VM successfully created
- ❌ **Deployment Failed**: Error details and troubleshooting

### Azure DevOps Pipeline Stages:
1. **Validate**: Check form inputs and parameters
2. **Deploy**: Execute Terraform to create VM
3. **Notify**: Send completion status to Teams

## 🛠️ Customization Options

### Form Validation Rules
The form includes client-side validation:
- Hospital code: 4 alphanumeric characters max
- Department code: 4 letters max  
- Real-time VM name preview
- Submit button enabled only when valid

### Terraform Variables
Easily customize VM specifications:
```hcl
vm_size = "Standard_D4s_v3"    # Change VM size
vm_location = "East US 2"      # Change region
vm_sku = "2022-datacenter"     # Change OS version
```

## 🚨 Troubleshooting

### Common Issues:

1. **Form Validation Fails**:
   - Check character limits (4 max for hospital/department)
   - Ensure required fields are filled

2. **Power Automate Flow Errors**:
   - Verify Form ID is correct
   - Check connection permissions
   - Validate Azure DevOps PAT token

3. **Pipeline Failures**:
   - Check variable group values
   - Verify service connection
   - Review Terraform state

4. **VM Name Conflicts**:
   - Check for existing VMs with same name
   - Consider adding timestamp suffix if needed

### Error Codes:
- **VAL001**: Hospital code validation failed
- **VAL002**: Department code validation failed  
- **DEP001**: Pipeline trigger failed
- **DEP002**: Terraform execution failed

## 📞 Support Process

1. **Level 1**: Form issues - User self-service with validation messages
2. **Level 2**: Deployment failures - Check Azure DevOps pipeline logs
3. **Level 3**: Infrastructure issues - Review Terraform state and Azure resources

## 🎉 Success Criteria

After setup, users should be able to:
1. Fill out the simple 4-field form
2. See real-time VM name preview
3. Submit form and receive immediate confirmation
4. Get Teams notification when deployment starts
5. Get Teams notification when VM is ready
6. Access the new VM within 10-15 minutes

**Zero manual intervention required after form submission!**

## 📂 Organized Deployment Structure

As requested, deployment files are organized into separate folders:

```
11-Microsoft-Forms-Enhanced/           # Main automation folder
├── vm-request-form-simplified.html   # User-facing form
├── power-automate-simplified-flow.json # Power Automate configuration
├── azure-pipelines-forms-automation.yml # DevOps pipeline
├── terraform-variables-enhanced.tf   # Enhanced Terraform variables
├── SETUP-GUIDE-COMPLETE.md          # Complete setup guide
└── deployment-scripts/              # PowerShell automation scripts
    └── Deploy-VM-Forms-Automation.ps1

10-Microsoft-Forms/                   # Original comprehensive setup
├── README.md                        # Detailed documentation
├── power-automate-microsoft-forms-flow.json
├── azure-pipelines-forms.yml
└── [other existing files...]

01-Terraform-Core/                   # Core Terraform files
├── main.tf                         # Main Terraform configuration
├── variable.tf                     # Variable definitions
└── terraform.tfstate              # State file

03-PowerShell-Scripts/              # Alternative deployment scripts
├── Deploy-VM-Auto.ps1             # Automated deployment
├── Deploy-VM-Interactive.ps1      # Interactive deployment
└── [other scripts...]
```

## 🔄 Workflow Comparison

### New Simplified Workflow (11-Microsoft-Forms-Enhanced/)
1. **Form**: 4 simple fields (Hospital, Department, Optional Names)
2. **Validation**: Real-time client-side validation
3. **Automation**: Direct Power Automate → DevOps → Terraform
4. **Naming**: `[Hospital][Department][Initials]VMCT` or `[Hospital][Department]VMCT`

### Original Comprehensive Workflow (10-Microsoft-Forms/)
1. **Form**: More detailed with VM type selection
2. **Validation**: Server-side validation with error handling
3. **Automation**: Power Automate → Azure Automation → Terraform
4. **Naming**: `[Hospital][Department][Initials]VM[Type]`

## 🚀 Quick Start Commands

### For New Users (Simplified):
```powershell
# Navigate to enhanced folder
cd "11-Microsoft-Forms-Enhanced"

# Deploy VM manually (for testing)
.\deployment-scripts\Deploy-VM-Forms-Automation.ps1 -VmName "CCH9SUPPJDVMCT" -HospitalCode "CCH9" -DepartmentCode "SUPP" -FirstName "John" -LastName "Doe"
```

### For Advanced Users (Original):
```powershell
# Navigate to original folder
cd "10-Microsoft-Forms"

# Deploy with full options
.\Deploy-VM-Microsoft-Forms.ps1 -HospitalCode "CCH9" -DepartmentCode "SUPP" -FirstName "John" -LastName "Doe" -VmType "CT" -VmCount 1
```

## 📋 Form Generation Workflow

**Question**: Do I need to change anything every time I generate a new form?

**Answer**: **No!** Once set up properly:

1. **Microsoft Form**: Create once, reuse forever
2. **Power Automate Flow**: Configure once with your Form ID
3. **Azure DevOps Pipeline**: Set up once with proper variables
4. **Terraform**: No changes needed for new requests

**The only thing that changes per request is the form data (Hospital, Department, Names), which automatically generates unique VM names and triggers deployment.**

## 🔧 Maintenance Requirements

### One-Time Setup:
- Create Microsoft Form (copy Form ID)
- Configure Power Automate Flow (paste Form ID)
- Set up Azure DevOps Pipeline
- Configure variable groups with secrets

### Zero Maintenance:
- Form submissions automatically trigger deployments
- VM names are automatically generated
- Terraform variables are automatically created
- Notifications are automatically sent

**This is truly a "set it and forget it" solution!**
