# Quick VM Deployment Script
# This script provides a simple interface for deploying VMs using the form parameters

param(
    [Parameter(Mandatory=$false)]
    [string]$VmName = "CCH9APPVMCT",
    
    [Parameter(Mandatory=$false)]
    [int]$VmCount = 1,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("plan", "apply", "destroy")]
    [string]$Action = "plan",
    
    [Parameter(Mandatory=$false)]
    [switch]$Interactive = $false
)

# Set console colors
$Host.UI.RawUI.BackgroundColor = "Black"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Azure VM Quick Deployment Tool" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the right directory
if (!(Test-Path "main.tf")) {
    Write-Host "ERROR: main.tf not found!" -ForegroundColor Red
    Write-Host "Please run this script from your Terraform directory." -ForegroundColor Yellow
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    Write-Host "Expected directory: D:\Maspects\MAHealth\Terraform\Scope_Demo-Environment" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Green
Write-Host ""

# Interactive mode
if ($Interactive) {
    $VmName = Read-Host "Enter VM Name (default: $VmName)"
    if ([string]::IsNullOrEmpty($VmName)) { $VmName = "CCH9APPVMCT" }
    
    $VmCountInput = Read-Host "Enter VM Count (default: $VmCount)"
    if (![string]::IsNullOrEmpty($VmCountInput)) { $VmCount = [int]$VmCountInput }
    
    $ActionInput = Read-Host "Enter action (plan/apply/destroy) [default: $Action]"
    if (![string]::IsNullOrEmpty($ActionInput)) { $Action = $ActionInput }
}

# Configuration summary
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Configuration Summary:" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "VM Name: $VmName" -ForegroundColor White
Write-Host "VM Count: $VmCount" -ForegroundColor White
Write-Host "Action: $Action" -ForegroundColor White
Write-Host "Resource Group: MAH9-SUP-CCH9-VMC" -ForegroundColor White
Write-Host "VM Size: Standard_D4s_v3" -ForegroundColor White
Write-Host "Location: East US 2" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Confirmation for destructive actions
if ($Action -eq "apply") {
    Write-Host "WARNING: This will create real Azure resources and may incur costs!" -ForegroundColor Yellow
    $confirm = Read-Host "Are you sure you want to proceed? (yes/no)"
    if ($confirm -ne "yes") {
        Write-Host "Deployment cancelled." -ForegroundColor Yellow
        exit 0
    }
}

if ($Action -eq "destroy") {
    Write-Host "WARNING: This will destroy all infrastructure!" -ForegroundColor Red
    $confirm = Read-Host "Are you sure you want to destroy resources? (yes/no)"
    if ($confirm -ne "yes") {
        Write-Host "Destroy cancelled." -ForegroundColor Yellow
        exit 0
    }
}

Write-Host ""
Write-Host "Starting deployment..." -ForegroundColor Green
Write-Host ""

# Check if Deploy-VM-Terraform.ps1 exists
if (!(Test-Path "Deploy-VM-Terraform.ps1")) {
    Write-Host "ERROR: Deploy-VM-Terraform.ps1 not found!" -ForegroundColor Red
    Write-Host "Please ensure the deployment script is in the current directory." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

try {
    # Execute the deployment script
    $params = @{
        Action = $Action
        VmName = $VmName
        VmCount = $VmCount
        ResourceGroupName = "MAH9-SUP-CCH9-VMC"
        VmSize = "Standard_D4s_v3"
        VmSku = "2022-datacenter"
        Location = "East US 2"
        AdminUsername = "maoperator"
        AdminPassword = "M1t1g@t0r2025"
        AzureDevOpsOrg = "https://dev.azure.com/MAHealth"
        TeamProject = "MAH9-SCP-CCH9GI"
        DeploymentGroup = "MAH9-SCP-CCH9-DGR-DEV"
        AzureDevOpsPat = "4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7"
        AgentFolder = "c:/Agent"
        AutoApprove = $true
    }
    
    & ".\Deploy-VM-Terraform.ps1" @params
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Deployment completed!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    
    if ($Action -eq "plan") {
        Write-Host "The plan has been generated. Review the output above." -ForegroundColor Cyan
        Write-Host "If everything looks good, run this script again with '-Action apply'" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Example: .\Quick-Deploy-VM.ps1 -Action apply -VmName $VmName -VmCount $VmCount" -ForegroundColor Yellow
    }
    
    if ($Action -eq "apply") {
        Write-Host "VM deployment completed! Check Azure portal to verify resources." -ForegroundColor Green
        Write-Host "VM Name: $VmName-1" -ForegroundColor White
        Write-Host "Resource Group: MAH9-SUP-CCH9-VMC" -ForegroundColor White
        Write-Host ""
        Write-Host "You can connect to your VM using:" -ForegroundColor Cyan
        Write-Host "- Azure Portal (Virtual Machines > $VmName-1 > Connect)" -ForegroundColor White
        Write-Host "- RDP (if public IP is assigned)" -ForegroundColor White
        Write-Host "- Azure Bastion (recommended for security)" -ForegroundColor White
    }
    
    if ($Action -eq "destroy") {
        Write-Host "Infrastructure destroyed successfully!" -ForegroundColor Green
        Write-Host "All resources have been removed from Azure." -ForegroundColor White
    }
    
} catch {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "Deployment failed!" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Ensure you're logged into Azure (az login)" -ForegroundColor White
    Write-Host "2. Verify Terraform is installed and in PATH" -ForegroundColor White
    Write-Host "3. Check Azure permissions for the resource group" -ForegroundColor White
    Write-Host "4. Review the error message above for specific issues" -ForegroundColor White
}

Write-Host ""
Read-Host "Press Enter to exit"
